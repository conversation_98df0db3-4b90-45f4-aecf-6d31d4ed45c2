"""
邮件相关API端点
"""

from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr

from app.services.email_service import email_service
from app.api.deps import require_permission

router = APIRouter()


class EmailRequest(BaseModel):
    """邮件发送请求"""
    to_emails: List[EmailStr]
    subject: str
    body: str
    html_body: Optional[str] = None
    cc_emails: Optional[List[EmailStr]] = None
    bcc_emails: Optional[List[EmailStr]] = None


class NotificationRequest(BaseModel):
    """通知邮件请求"""
    notification_type: str
    recipients: List[EmailStr]
    data: Dict[str, Any]


class EmailConfigRequest(BaseModel):
    """邮件配置请求"""
    smtp_host: str
    smtp_port: int
    smtp_user: str
    smtp_password: str
    smtp_tls: bool = True
    smtp_ssl: bool = False
    email_from: str
    enabled: bool = True


@router.post("/send", summary="发送邮件")
async def send_email(
    email_request: EmailRequest,
    current_user: dict = Depends(require_permission("email.send"))
):
    """发送邮件"""
    try:
        success = await email_service.send_email_async(
            to_emails=email_request.to_emails,
            subject=email_request.subject,
            body=email_request.body,
            html_body=email_request.html_body,
            cc_emails=email_request.cc_emails,
            bcc_emails=email_request.bcc_emails
        )
        
        if success:
            return {"message": "邮件发送成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="邮件发送失败"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"邮件发送失败: {str(e)}"
        )


@router.post("/notification", summary="发送通知邮件")
async def send_notification(
    notification_request: NotificationRequest,
    current_user: dict = Depends(require_permission("email.send"))
):
    """发送通知邮件"""
    try:
        success = email_service.send_notification(
            notification_type=notification_request.notification_type,
            recipients=notification_request.recipients,
            data=notification_request.data
        )
        
        if success:
            return {"message": "通知邮件发送成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="通知邮件发送失败"
            )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"通知邮件发送失败: {str(e)}"
        )


@router.get("/test", summary="测试邮件连接")
async def test_email_connection(
    current_user: dict = Depends(require_permission("email.config"))
):
    """测试邮件连接"""
    result = email_service.test_connection()
    
    if result["success"]:
        return result
    else:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=result["message"]
        )


@router.post("/config", summary="配置邮件设置")
async def configure_email(
    config_request: EmailConfigRequest,
    current_user: dict = Depends(require_permission("email.config"))
):
    """配置邮件设置"""
    try:
        email_service.update_config(config_request.dict())
        return {"message": "邮件配置更新成功"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"邮件配置更新失败: {str(e)}"
        )


@router.get("/config", summary="获取邮件配置")
async def get_email_config(
    current_user: dict = Depends(require_permission("email.config"))
):
    """获取邮件配置（隐藏敏感信息）"""
    return {
        "smtp_host": email_service.smtp_host,
        "smtp_port": email_service.smtp_port,
        "smtp_user": email_service.smtp_user,
        "smtp_tls": email_service.smtp_tls,
        "smtp_ssl": email_service.smtp_ssl,
        "email_from": email_service.email_from,
        "enabled": email_service.enabled
    }


@router.get("/templates", summary="获取邮件模板列表")
async def get_email_templates(
    current_user: dict = Depends(require_permission("email.view"))
):
    """获取可用的邮件模板列表"""
    templates = [
        {
            "type": "anomaly_alert",
            "name": "异常警报",
            "description": "系统检测到异常时发送的警报邮件"
        },
        {
            "type": "report_ready",
            "name": "报告就绪",
            "description": "报告生成完成后发送的通知邮件"
        },
        {
            "type": "system_alert",
            "name": "系统警报",
            "description": "系统监控发现问题时发送的警报邮件"
        },
        {
            "type": "backup_complete",
            "name": "备份完成",
            "description": "系统备份完成后发送的通知邮件"
        }
    ]
    
    return {"templates": templates}
