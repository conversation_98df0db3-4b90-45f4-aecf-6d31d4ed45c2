"""
简化的UI优化功能检查脚本
"""

import sys
import os

# 添加frontend路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'frontend'))

def check_navigation_component():
    """检查导航组件"""
    try:
        from components.navigation import ModernNavigation
        
        # 检查关键方法是否存在
        methods = [
            'create_breadcrumb',
            'create_quick_actions', 
            'track_page_visit',
            'create_recent_pages_sidebar',
            'create_smart_navigation_widget'
        ]
        
        for method in methods:
            if hasattr(ModernNavigation, method):
                print(f"✅ ModernNavigation.{method} 存在")
            else:
                print(f"❌ ModernNavigation.{method} 不存在")
                return False
        
        print("✅ 导航组件检查通过")
        return True
        
    except ImportError as e:
        print(f"❌ 导航组件导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 导航组件检查失败: {e}")
        return False

def check_chart_utils():
    """检查图表工具"""
    try:
        from utils.chart_utils import (
            create_enhanced_realtime_dashboard,
            create_enhanced_interactive_gantt,
            create_enhanced_equipment_heatmap,
            create_enhanced_production_flow_chart,
            create_enhanced_quality_trend_chart,
            create_enhanced_cost_analysis_chart,
            create_enhanced_performance_radar_chart,
            create_enhanced_alert_timeline
        )
        
        functions = [
            'create_enhanced_realtime_dashboard',
            'create_enhanced_interactive_gantt',
            'create_enhanced_equipment_heatmap',
            'create_enhanced_production_flow_chart',
            'create_enhanced_quality_trend_chart',
            'create_enhanced_cost_analysis_chart',
            'create_enhanced_performance_radar_chart',
            'create_enhanced_alert_timeline'
        ]
        
        for func_name in functions:
            print(f"✅ {func_name} 导入成功")
        
        print("✅ 图表工具检查通过")
        return True
        
    except ImportError as e:
        print(f"❌ 图表工具导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 图表工具检查失败: {e}")
        return False

def check_unified_ai_service():
    """检查统一AI服务"""
    try:
        from services.unified_ai_service import UnifiedAIService, AIServiceType
        
        # 创建服务实例
        ai_service = UnifiedAIService()
        
        # 检查UI增强服务是否可用
        ui_enhancement_available = ai_service.service_status.get(AIServiceType.UI_ENHANCEMENT, False)
        
        print(f"✅ 统一AI服务创建成功")
        print(f"✅ UI增强服务可用: {ui_enhancement_available}")
        
        # 检查所有服务状态
        print("\n📊 AI服务状态:")
        for service_type, status in ai_service.service_status.items():
            status_icon = "✅" if status else "❌"
            print(f"  {status_icon} {service_type.value}: {status}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 统一AI服务导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 统一AI服务检查失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    dependencies = [
        'streamlit',
        'plotly',
        'pandas',
        'numpy'
    ]
    
    print("📦 检查依赖项:")
    all_available = True
    
    for dep in dependencies:
        try:
            __import__(dep)
            print(f"✅ {dep} 可用")
        except ImportError:
            print(f"❌ {dep} 不可用")
            all_available = False
    
    return all_available

def main():
    """主检查函数"""
    print("🔍 Smart APS UI优化功能检查")
    print("=" * 50)
    
    results = []
    
    # 1. 检查依赖项
    print("\n1. 依赖项检查")
    print("-" * 20)
    dep_result = check_dependencies()
    results.append(("依赖项", dep_result))
    
    # 2. 检查导航组件
    print("\n2. 导航组件检查")
    print("-" * 20)
    nav_result = check_navigation_component()
    results.append(("导航组件", nav_result))
    
    # 3. 检查图表工具
    print("\n3. 图表工具检查")
    print("-" * 20)
    chart_result = check_chart_utils()
    results.append(("图表工具", chart_result))
    
    # 4. 检查统一AI服务
    print("\n4. 统一AI服务检查")
    print("-" * 20)
    ai_result = check_unified_ai_service()
    results.append(("统一AI服务", ai_result))
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 检查结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("🎉 所有检查都通过！UI优化功能可以正常使用。")
        return True
    else:
        print("⚠️ 部分检查失败，请检查相关组件。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
