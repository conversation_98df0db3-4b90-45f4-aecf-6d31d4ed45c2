"""
增强图表工具函数
集成智能数据可视化功能
"""

import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
import streamlit as st
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from config.theme import apply_plotly_theme
from config.settings import CHART_CONFIG


def create_gantt_chart(
    gantt_data: Dict[str, Any],
    view_mode: str = "按设备",
    time_unit: str = "天",
    show_critical_path: bool = True,
    show_delays: bool = True,
    height: int = 600
) -> go.Figure:
    """创建甘特图"""

    tasks = gantt_data.get("tasks", [])
    if not tasks:
        # 返回空图表
        fig = go.Figure()
        fig.add_annotation(
            text="暂无数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=20, color="gray")
        )
        return apply_plotly_theme(fig)


def create_enhanced_realtime_dashboard(data: Dict[str, Any]) -> None:
    """创建增强的实时仪表板（集成到现有主题系统）"""
    st.markdown("### 📊 实时生产监控")

    # 使用现有的响应式布局
    from components.responsive_layout import ResponsiveLayout

    # 创建响应式指标网格
    metrics_data = [
        {
            "title": "🏭 生产效率",
            "value": f"{data.get('efficiency', 85)}%",
            "delta": f"{data.get('efficiency_delta', 2)}%",
            "delta_color": "positive" if data.get('efficiency_delta', 2) >= 0 else "negative"
        },
        {
            "title": "⚙️ 设备利用率",
            "value": f"{data.get('utilization', 78)}%",
            "delta": f"{data.get('utilization_delta', -1)}%",
            "delta_color": "negative" if data.get('utilization_delta', -1) < 0 else "positive"
        },
        {
            "title": "✅ 质量合格率",
            "value": f"{data.get('quality', 96)}%",
            "delta": f"{data.get('quality_delta', 1)}%",
            "delta_color": "positive" if data.get('quality_delta', 1) >= 0 else "negative"
        },
        {
            "title": "📦 产量完成率",
            "value": f"{data.get('output', 102)}%",
            "delta": f"{data.get('output_delta', 5)}%",
            "delta_color": "positive" if data.get('output_delta', 5) >= 0 else "negative"
        }
    ]

    # 使用现有的UI主题创建指标卡片
    from utils.ui_theme import SmartAPSTheme

    cols = st.columns(4)
    for i, metric in enumerate(metrics_data):
        with cols[i]:
            # 使用现有主题的指标卡片
            delta_color = "green" if metric["delta_color"] == "positive" else "red"
            st.metric(
                label=metric["title"],
                value=metric["value"],
                delta=metric["delta"]
            )


def create_enhanced_interactive_gantt(tasks_data: List[Dict[str, Any]]) -> go.Figure:
    """创建增强的交互式甘特图（基于现有甘特图功能）"""
    if not tasks_data:
        st.warning("暂无任务数据")
        return None

    df = pd.DataFrame(tasks_data)

    # 确保必要的列存在
    required_columns = ['task_name', 'start_date', 'end_date', 'status']
    for col in required_columns:
        if col not in df.columns:
            st.error(f"缺少必要的列: {col}")
            return None

    # 创建增强的甘特图
    fig = px.timeline(
        df,
        x_start="start_date",
        x_end="end_date",
        y="task_name",
        color="status",
        title="📅 智能生产计划甘特图",
        color_discrete_map={
            "进行中": "#4CAF50",
            "计划中": "#2196F3",
            "已完成": "#9E9E9E",
            "延期": "#F44336",
            "暂停": "#FF9800"
        },
        hover_data=["task_name", "status"]
    )

    # 增强交互性
    fig.update_layout(
        height=500,
        xaxis_title="时间轴",
        yaxis_title="生产任务",
        showlegend=True,
        hovermode='closest',
        # 添加交互工具
        dragmode='pan'
    )

    # 添加今日线
    today = datetime.now()
    fig.add_vline(
        x=today,
        line_dash="dash",
        line_color="red",
        annotation_text="今日",
        annotation_position="top"
    )

    return apply_plotly_theme(fig)


def create_enhanced_equipment_heatmap(equipment_data: Dict[str, List[float]]) -> go.Figure:
    """创建增强的设备状态热力图"""
    if not equipment_data:
        st.warning("暂无设备数据")
        return None

    # 准备数据
    equipment_names = list(equipment_data.keys())
    hours = list(range(24))

    # 创建热力图数据
    z_data = []
    for equipment in equipment_names:
        data_points = equipment_data[equipment]
        # 确保数据长度为24小时
        if len(data_points) < 24:
            data_points.extend([0] * (24 - len(data_points)))
        elif len(data_points) > 24:
            data_points = data_points[:24]
        z_data.append(data_points)

    fig = go.Figure(data=go.Heatmap(
        z=z_data,
        x=[f"{h:02d}:00" for h in hours],
        y=equipment_names,
        colorscale='RdYlGn',
        reversescale=True,
        colorbar=dict(title="利用率 %"),
        hoverongaps=False,
        hovertemplate='设备: %{y}<br>时间: %{x}<br>利用率: %{z}%<extra></extra>'
    ))

    fig.update_layout(
        title="🔥 设备24小时利用率热力图",
        xaxis_title="时间",
        yaxis_title="设备",
        height=400
    )

    return apply_plotly_theme(fig)


def create_enhanced_production_flow_chart(flow_data: List[Dict[str, Any]]) -> go.Figure:
    """创建增强的生产流程图（桑基图）"""
    if not flow_data:
        st.warning("暂无流程数据")
        return None

    # 创建桑基图
    labels = []
    sources = []
    targets = []
    values = []

    for item in flow_data:
        if 'source' not in item or 'target' not in item or 'value' not in item:
            continue

        if item['source'] not in labels:
            labels.append(item['source'])
        if item['target'] not in labels:
            labels.append(item['target'])

        sources.append(labels.index(item['source']))
        targets.append(labels.index(item['target']))
        values.append(item['value'])

    if not sources:
        st.warning("流程数据格式不正确")
        return None

    fig = go.Figure(data=[go.Sankey(
        node=dict(
            pad=15,
            thickness=20,
            line=dict(color="black", width=0.5),
            label=labels,
            color="#4A90E2"
        ),
        link=dict(
            source=sources,
            target=targets,
            value=values,
            hovertemplate='从 %{source.label} 到 %{target.label}<br>数量: %{value}<extra></extra>'
        )
    )])

    fig.update_layout(
        title_text="🔄 智能生产流程图",
        font_size=12,
        height=500
    )

    return apply_plotly_theme(fig)


def create_enhanced_quality_trend_chart(quality_data: pd.DataFrame) -> go.Figure:
    """创建增强的质量趋势图"""
    if quality_data.empty:
        st.warning("暂无质量数据")
        return None

    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('质量指标趋势', '缺陷分布', '质量等级分布', '改进趋势'),
        specs=[[{"secondary_y": True}, {"type": "pie"}],
               [{"type": "bar"}, {"secondary_y": True}]],
        vertical_spacing=0.12,
        horizontal_spacing=0.1
    )

    # 1. 质量趋势线
    if 'date' in quality_data.columns and 'quality_rate' in quality_data.columns:
        fig.add_trace(
            go.Scatter(
                x=quality_data['date'],
                y=quality_data['quality_rate'],
                mode='lines+markers',
                name='质量合格率',
                line=dict(color='#4CAF50', width=3),
                hovertemplate='日期: %{x}<br>合格率: %{y}%<extra></extra>'
            ),
            row=1, col=1
        )

        # 添加目标线
        target_line = [95] * len(quality_data)
        fig.add_trace(
            go.Scatter(
                x=quality_data['date'],
                y=target_line,
                mode='lines',
                name='目标线',
                line=dict(color='red', dash='dash'),
                hovertemplate='目标: %{y}%<extra></extra>'
            ),
            row=1, col=1
        )

    # 2. 缺陷分布饼图
    if 'defect_type' in quality_data.columns and 'count' in quality_data.columns:
        defect_data = quality_data.groupby('defect_type')['count'].sum()
        fig.add_trace(
            go.Pie(
                labels=defect_data.index,
                values=defect_data.values,
                name="缺陷分布",
                hovertemplate='缺陷类型: %{label}<br>数量: %{value}<br>占比: %{percent}<extra></extra>'
            ),
            row=1, col=2
        )

    # 3. 质量等级分布
    if 'quality_grade' in quality_data.columns:
        grade_counts = quality_data['quality_grade'].value_counts()
        fig.add_trace(
            go.Bar(
                x=grade_counts.index,
                y=grade_counts.values,
                name='质量等级',
                marker_color='#2196F3',
                hovertemplate='等级: %{x}<br>数量: %{y}<extra></extra>'
            ),
            row=2, col=1
        )

    # 4. 改进趋势
    if 'improvement_score' in quality_data.columns:
        fig.add_trace(
            go.Scatter(
                x=quality_data['date'],
                y=quality_data['improvement_score'],
                mode='lines+markers',
                name='改进分数',
                line=dict(color='#FF9800', width=2),
                hovertemplate='日期: %{x}<br>改进分数: %{y}<extra></extra>'
            ),
            row=2, col=2
        )

    fig.update_layout(
        height=700,
        showlegend=True,
        title_text="📈 智能质量分析报告"
    )

    return apply_plotly_theme(fig)


def create_enhanced_cost_analysis_chart(cost_data: Dict[str, float]) -> go.Figure:
    """创建增强的成本分析瀑布图"""
    if not cost_data:
        st.warning("暂无成本数据")
        return None

    categories = list(cost_data.keys())
    values = list(cost_data.values())

    # 计算累计值用于瀑布图
    cumulative = []
    running_total = 0
    for i, value in enumerate(values):
        if i == 0:
            cumulative.append(value)
            running_total = value
        elif i == len(values) - 1:  # 最后一个是总计
            cumulative.append(running_total)
        else:
            cumulative.append(value)
            running_total += value

    # 创建瀑布图
    fig = go.Figure(go.Waterfall(
        name="成本分析",
        orientation="v",
        measure=["relative"] * (len(categories) - 1) + ["total"],
        x=categories,
        textposition="outside",
        text=[f"¥{v:,.0f}" for v in values],
        y=values,
        connector={"line": {"color": "rgb(63, 63, 63)"}},
        increasing={"marker": {"color": "#F44336"}},
        decreasing={"marker": {"color": "#4CAF50"}},
        totals={"marker": {"color": "#2196F3"}},
        hovertemplate='类别: %{x}<br>金额: ¥%{y:,.0f}<extra></extra>'
    ))

    fig.update_layout(
        title="💰 智能成本分析瀑布图",
        showlegend=False,
        height=500,
        xaxis_title="成本类别",
        yaxis_title="金额 (¥)"
    )

    return apply_plotly_theme(fig)


def create_enhanced_performance_radar_chart(performance_data: Dict[str, float]) -> go.Figure:
    """创建增强的性能雷达图"""
    if not performance_data:
        st.warning("暂无性能数据")
        return None

    categories = list(performance_data.keys())
    values = list(performance_data.values())

    fig = go.Figure()

    # 当前性能
    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='当前性能',
        line_color='#4A90E2',
        fillcolor='rgba(74, 144, 226, 0.3)',
        hovertemplate='指标: %{theta}<br>得分: %{r}<extra></extra>'
    ))

    # 目标性能线
    target_values = [90] * len(categories)  # 假设目标都是90%
    fig.add_trace(go.Scatterpolar(
        r=target_values,
        theta=categories,
        fill='toself',
        name='目标性能',
        line_color='#50C878',
        fillcolor='rgba(80, 200, 120, 0.2)',
        hovertemplate='指标: %{theta}<br>目标: %{r}<extra></extra>'
    ))

    # 行业平均线
    industry_avg = [75] * len(categories)  # 假设行业平均是75%
    fig.add_trace(go.Scatterpolar(
        r=industry_avg,
        theta=categories,
        fill='toself',
        name='行业平均',
        line_color='#FF9800',
        fillcolor='rgba(255, 152, 0, 0.1)',
        hovertemplate='指标: %{theta}<br>行业平均: %{r}<extra></extra>'
    ))

    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100],
                ticksuffix="%"
            )),
        showlegend=True,
        title="🎯 智能综合性能雷达图",
        height=500
    )

    return apply_plotly_theme(fig)


def create_enhanced_alert_timeline(alerts_data: List[Dict[str, Any]]) -> go.Figure:
    """创建增强的警报时间线"""
    if not alerts_data:
        st.info("🎉 暂无警报，系统运行正常")
        return None

    df = pd.DataFrame(alerts_data)

    # 确保时间戳格式正确
    if 'timestamp' in df.columns:
        df['timestamp'] = pd.to_datetime(df['timestamp'])
    else:
        st.error("警报数据缺少时间戳")
        return None

    # 按严重程度分组
    severity_colors = {
        'high': '#F44336',
        'medium': '#FF9800',
        'low': '#4CAF50',
        'info': '#2196F3'
    }

    severity_symbols = {
        'high': 'triangle-up',
        'medium': 'circle',
        'low': 'square',
        'info': 'diamond'
    }

    fig = go.Figure()

    for severity in ['high', 'medium', 'low', 'info']:
        if 'severity' in df.columns:
            severity_data = df[df['severity'] == severity]
        else:
            continue

        if not severity_data.empty:
            fig.add_trace(go.Scatter(
                x=severity_data['timestamp'],
                y=[severity] * len(severity_data),
                mode='markers',
                marker=dict(
                    size=12,
                    color=severity_colors[severity],
                    symbol=severity_symbols[severity],
                    line=dict(width=1, color='white')
                ),
                name=f'{severity.upper()} 级警报',
                text=severity_data.get('message', ''),
                hovertemplate='<b>%{text}</b><br>时间: %{x}<br>级别: %{y}<extra></extra>'
            ))

    fig.update_layout(
        title="⚠️ 智能警报时间线",
        xaxis_title="时间",
        yaxis_title="严重程度",
        height=400,
        showlegend=True,
        hovermode='closest'
    )

    return apply_plotly_theme(fig)


def create_resource_utilization_chart(
    utilization_data: Dict[str, List[float]],
    height: int = 400
) -> go.Figure:
    """创建资源利用率图表"""

    if not utilization_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无利用率数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)

    fig = go.Figure()

    # 为每个资源添加折线
    for resource, utilization in utilization_data.items():
        fig.add_trace(go.Scatter(
            x=list(range(len(utilization))),
            y=utilization,
            mode='lines+markers',
            name=resource,
            line=dict(width=3),
            marker=dict(size=8)
        ))

    # 添加平均利用率线
    all_values = [val for values in utilization_data.values() for val in values]
    avg_utilization = sum(all_values) / len(all_values)

    fig.add_hline(
        y=avg_utilization,
        line_dash="dash",
        line_color="gray",
        annotation_text=f"平均利用率: {avg_utilization:.1f}%"
    )

    fig.update_layout(
        title="资源利用率趋势",
        xaxis_title="时间段",
        yaxis_title="利用率 (%)",
        height=height,
        yaxis=dict(range=[0, 100])
    )

    return apply_plotly_theme(fig)


def create_production_dashboard(
    production_data: Dict[str, Any],
    height: int = 800
) -> go.Figure:
    """创建生产仪表盘"""

    # 创建子图
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=("生产进度", "设备状态", "质量指标", "效率趋势"),
        specs=[
            [{"type": "indicator"}, {"type": "pie"}],
            [{"type": "bar"}, {"type": "scatter"}]
        ]
    )

    # 生产进度指示器
    progress = production_data.get("progress", 0)
    fig.add_trace(
        go.Indicator(
            mode="gauge+number+delta",
            value=progress,
            domain={'x': [0, 1], 'y': [0, 1]},
            title={'text': "完成进度 (%)"},
            delta={'reference': 80},
            gauge={
                'axis': {'range': [None, 100]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 50], 'color': "lightgray"},
                    {'range': [50, 80], 'color': "gray"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 90
                }
            }
        ),
        row=1, col=1
    )

    # 设备状态饼图
    equipment_status = production_data.get("equipment_status", {})
    if equipment_status:
        fig.add_trace(
            go.Pie(
                labels=list(equipment_status.keys()),
                values=list(equipment_status.values()),
                name="设备状态"
            ),
            row=1, col=2
        )

    # 质量指标柱状图
    quality_metrics = production_data.get("quality_metrics", {})
    if quality_metrics:
        fig.add_trace(
            go.Bar(
                x=list(quality_metrics.keys()),
                y=list(quality_metrics.values()),
                name="质量指标",
                marker_color="green"
            ),
            row=2, col=1
        )

    # 效率趋势线图
    efficiency_trend = production_data.get("efficiency_trend", {})
    if efficiency_trend:
        fig.add_trace(
            go.Scatter(
                x=efficiency_trend.get("dates", []),
                y=efficiency_trend.get("values", []),
                mode='lines+markers',
                name="效率趋势",
                line=dict(color="blue", width=3)
            ),
            row=2, col=2
        )

    fig.update_layout(
        height=height,
        title_text="生产监控仪表盘",
        showlegend=False
    )

    return apply_plotly_theme(fig)


def create_equipment_oee_chart(
    equipment_data: List[Dict[str, Any]],
    height: int = 400
) -> go.Figure:
    """创建设备OEE图表"""

    if not equipment_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无设备数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)

    df = pd.DataFrame(equipment_data)

    # 创建OEE分解图
    fig = go.Figure()

    # 可用率
    fig.add_trace(go.Bar(
        name='可用率',
        x=df['name'],
        y=df.get('availability', [85] * len(df)),
        marker_color='lightblue'
    ))

    # 性能率
    fig.add_trace(go.Bar(
        name='性能率',
        x=df['name'],
        y=df.get('performance', [90] * len(df)),
        marker_color='lightgreen'
    ))

    # 质量率
    fig.add_trace(go.Bar(
        name='质量率',
        x=df['name'],
        y=df.get('quality', [95] * len(df)),
        marker_color='lightyellow'
    ))

    # OEE总值
    fig.add_trace(go.Scatter(
        name='OEE',
        x=df['name'],
        y=df.get('oee', [72] * len(df)),
        mode='lines+markers',
        line=dict(color='red', width=3),
        marker=dict(size=10),
        yaxis='y2'
    ))

    fig.update_layout(
        title='设备OEE分析',
        xaxis_title='设备',
        yaxis_title='百分比 (%)',
        yaxis2=dict(
            title='OEE (%)',
            overlaying='y',
            side='right'
        ),
        height=height,
        barmode='group'
    )

    return apply_plotly_theme(fig)


def create_order_status_chart(
    order_data: List[Dict[str, Any]],
    chart_type: str = "pie",
    height: int = 400
) -> go.Figure:
    """创建订单状态图表"""

    if not order_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无订单数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)

    # 统计订单状态
    status_counts = {}
    for order in order_data:
        status = order.get('status', '未知')
        status_counts[status] = status_counts.get(status, 0) + 1

    if chart_type == "pie":
        fig = px.pie(
            values=list(status_counts.values()),
            names=list(status_counts.keys()),
            title="订单状态分布"
        )
    else:  # bar
        fig = px.bar(
            x=list(status_counts.keys()),
            y=list(status_counts.values()),
            title="订单状态统计",
            color=list(status_counts.keys())
        )

    fig.update_layout(height=height)

    return apply_plotly_theme(fig)


def create_capacity_analysis_chart(
    capacity_data: Dict[str, Any],
    height: int = 500
) -> go.Figure:
    """创建产能分析图表"""

    if not capacity_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无产能数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)

    # 创建子图
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=("产能利用率", "产能缺口分析"),
        specs=[[{"secondary_y": True}, {"type": "bar"}]]
    )

    # 产能利用率
    dates = capacity_data.get("dates", [])
    planned_capacity = capacity_data.get("planned_capacity", [])
    actual_capacity = capacity_data.get("actual_capacity", [])

    fig.add_trace(
        go.Scatter(
            x=dates,
            y=planned_capacity,
            mode='lines',
            name='计划产能',
            line=dict(color='blue', dash='dash')
        ),
        row=1, col=1
    )

    fig.add_trace(
        go.Scatter(
            x=dates,
            y=actual_capacity,
            mode='lines+markers',
            name='实际产能',
            line=dict(color='green')
        ),
        row=1, col=1
    )

    # 产能缺口
    equipment_names = capacity_data.get("equipment_names", [])
    capacity_gaps = capacity_data.get("capacity_gaps", [])

    fig.add_trace(
        go.Bar(
            x=equipment_names,
            y=capacity_gaps,
            name='产能缺口',
            marker_color=['red' if gap > 0 else 'green' for gap in capacity_gaps]
        ),
        row=1, col=2
    )

    fig.update_layout(
        height=height,
        title_text="产能分析"
    )

    return apply_plotly_theme(fig)


def create_kpi_indicators(
    kpi_data: Dict[str, Any],
    layout: str = "horizontal"
) -> go.Figure:
    """创建KPI指标图表"""

    if not kpi_data:
        fig = go.Figure()
        fig.add_annotation(
            text="暂无KPI数据",
            xref="paper", yref="paper",
            x=0.5, y=0.5,
            showarrow=False,
            font=dict(size=16, color="gray")
        )
        return apply_plotly_theme(fig)

    kpis = kpi_data.get("kpis", [])

    if layout == "horizontal":
        cols = len(kpis)
        rows = 1
    else:
        cols = 2
        rows = (len(kpis) + 1) // 2

    fig = make_subplots(
        rows=rows,
        cols=cols,
        specs=[[{"type": "indicator"}] * cols] * rows,
        subplot_titles=[kpi["name"] for kpi in kpis]
    )

    for i, kpi in enumerate(kpis):
        row = i // cols + 1
        col = i % cols + 1

        fig.add_trace(
            go.Indicator(
                mode="number+delta+gauge",
                value=kpi["value"],
                delta={"reference": kpi.get("target", kpi["value"])},
                gauge={
                    "axis": {"range": [0, kpi.get("max_value", 100)]},
                    "bar": {"color": kpi.get("color", "darkblue")},
                    "steps": [
                        {"range": [0, kpi.get("target", 50)], "color": "lightgray"},
                    ],
                    "threshold": {
                        "line": {"color": "red", "width": 4},
                        "thickness": 0.75,
                        "value": kpi.get("target", 80)
                    }
                },
                title={"text": kpi["name"]},
                domain={"row": row-1, "column": col-1}
            ),
            row=row, col=col
        )

    fig.update_layout(
        height=300 * rows,
        title_text="关键绩效指标"
    )

    return apply_plotly_theme(fig)
