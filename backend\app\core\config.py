"""
系统配置管理
支持环境变量和配置文件
"""

import os
from typing import List, Optional, Union
from datetime import datetime
from pydantic import BaseSettings, validator
from functools import lru_cache


class Settings(BaseSettings):
    """系统配置类"""

    # 基础配置
    PROJECT_NAME: str = "Smart APS"
    VERSION: str = "1.0.0"
    DEBUG: bool = False
    HOST: str = "0.0.0.0"
    PORT: int = 8000

    # API配置
    API_V1_STR: str = "/api/v1"
    ALLOWED_HOSTS: List[str] = ["*"]

    # 主数据库配置 (MySQL)
    DATABASE_URL: Optional[str] = None
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "smart_aps"
    DB_PASSWORD: str = "smart_aps_password"
    DB_NAME: str = "smart_aps"

    # 多数据库配置
    # 库存数据库配置
    INVENTORY_DB_URL: Optional[str] = None
    INVENTORY_DB_HOST: str = "localhost"
    INVENTORY_DB_PORT: int = 3306
    INVENTORY_DB_USER: str = "inventory_user"
    INVENTORY_DB_PASSWORD: str = "inventory_password"
    INVENTORY_DB_NAME: str = "inventory_db"

    # 生产数据分析数据库配置
    PRODUCTION_ANALYSIS_DB_URL: Optional[str] = None
    PRODUCTION_ANALYSIS_DB_HOST: str = "localhost"
    PRODUCTION_ANALYSIS_DB_PORT: int = 3306
    PRODUCTION_ANALYSIS_DB_USER: str = "production_user"
    PRODUCTION_ANALYSIS_DB_PASSWORD: str = "production_password"
    PRODUCTION_ANALYSIS_DB_NAME: str = "data_analysis"

    # ERP系统数据库配置
    ERP_DB_URL: Optional[str] = None
    ERP_DB_HOST: str = "localhost"
    ERP_DB_PORT: int = 3306
    ERP_DB_USER: str = "erp_user"
    ERP_DB_PASSWORD: str = "erp_password"
    ERP_DB_NAME: str = "erp_system"

    # MES系统数据库配置
    MES_DB_URL: Optional[str] = None
    MES_DB_HOST: str = "localhost"
    MES_DB_PORT: int = 3306
    MES_DB_USER: str = "mes_user"
    MES_DB_PASSWORD: str = "mes_password"
    MES_DB_NAME: str = "mes_system"

    # PCI数据库配置
    PCI_DB_URL: Optional[str] = None
    PCI_DB_HOST: str = "localhost"
    PCI_DB_PORT: int = 3306
    PCI_DB_USER: str = "pci_user"
    PCI_DB_PASSWORD: str = "pci_password"
    PCI_DB_NAME: str = "pci_system"
    PCI_DB_ENGINE: str = "mysql"

    # 质量数据库配置
    QUALITY_DB_URL: Optional[str] = None
    QUALITY_DB_HOST: str = "localhost"
    QUALITY_DB_PORT: int = 3306
    QUALITY_DB_USER: str = "quality_user"
    QUALITY_DB_PASSWORD: str = "quality_password"
    QUALITY_DB_NAME: str = "quality_system"
    QUALITY_DB_ENGINE: str = "mysql"

    # 物流数据库配置
    LOGISTICS_DB_URL: Optional[str] = None
    LOGISTICS_DB_HOST: str = "localhost"
    LOGISTICS_DB_PORT: int = 3306
    LOGISTICS_DB_USER: str = "logistics_user"
    LOGISTICS_DB_PASSWORD: str = "logistics_password"
    LOGISTICS_DB_NAME: str = "logistics_system"
    LOGISTICS_DB_ENGINE: str = "mysql"

    # Oracle数据库示例配置
    ORACLE_DB_URL: Optional[str] = None
    ORACLE_DB_HOST: str = "localhost"
    ORACLE_DB_PORT: int = 1521
    ORACLE_DB_USER: str = "oracle_user"
    ORACLE_DB_PASSWORD: str = "oracle_password"
    ORACLE_DB_NAME: str = "ORCL"  # Oracle服务名
    ORACLE_DB_ENGINE: str = "oracle"

    # 自定义数据库配置
    CUSTOM1_DB_URL: Optional[str] = None
    CUSTOM1_DB_HOST: str = "localhost"
    CUSTOM1_DB_PORT: int = 3306
    CUSTOM1_DB_USER: str = "custom1_user"
    CUSTOM1_DB_PASSWORD: str = "custom1_password"
    CUSTOM1_DB_NAME: str = "custom1_db"
    CUSTOM1_DB_ENGINE: str = "mysql"

    CUSTOM2_DB_URL: Optional[str] = None
    CUSTOM2_DB_HOST: str = "localhost"
    CUSTOM2_DB_PORT: int = 3306
    CUSTOM2_DB_USER: str = "custom2_user"
    CUSTOM2_DB_PASSWORD: str = "custom2_password"
    CUSTOM2_DB_NAME: str = "custom2_db"
    CUSTOM2_DB_ENGINE: str = "mysql"

    CUSTOM3_DB_URL: Optional[str] = None
    CUSTOM3_DB_HOST: str = "localhost"
    CUSTOM3_DB_PORT: int = 3306
    CUSTOM3_DB_USER: str = "custom3_user"
    CUSTOM3_DB_PASSWORD: str = "custom3_password"
    CUSTOM3_DB_NAME: str = "custom3_db"
    CUSTOM3_DB_ENGINE: str = "mysql"

    # Redis配置
    REDIS_URL: Optional[str] = None
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None

    # JWT配置
    SECRET_KEY: str = "smart-aps-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # 文件上传配置
    UPLOAD_DIR: str = "uploads"
    MAX_UPLOAD_SIZE: int = 200 * 1024 * 1024  # 200MB
    ALLOWED_FILE_TYPES: List[str] = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # xlsx
        "application/vnd.ms-excel",  # xls
        "text/csv",  # csv
        "message/rfc822",  # eml
        "application/vnd.ms-outlook"  # msg
    ]

    # LLM配置
    # Ollama配置
    OLLAMA_BASE_URL: str = "http://localhost:11434"
    OLLAMA_MODEL: str = "llama2"
    OLLAMA_TIMEOUT: int = 60

    # Azure OpenAI配置
    AZURE_OPENAI_ENDPOINT: Optional[str] = None
    AZURE_OPENAI_API_KEY: Optional[str] = None
    AZURE_OPENAI_API_VERSION: str = "2023-12-01-preview"
    AZURE_OPENAI_DEPLOYMENT_NAME: str = "gpt-4"

    # 默认使用的LLM服务
    DEFAULT_LLM_SERVICE: str = "ollama"  # ollama 或 azure

    # 缓存配置
    CACHE_TTL: int = 300  # 5分钟
    CACHE_MAX_SIZE: int = 1000

    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/smart_aps.log"
    LOG_MAX_SIZE: int = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT: int = 5

    # 安全配置
    ENCRYPTION_KEY: Optional[str] = None
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_WINDOW: int = 60  # 秒

    # LDAP配置
    LDAP_ENABLED: bool = False
    LDAP_SERVER_URI: str = "ldap://localhost:389"
    LDAP_SERVER_PORT: int = 389
    LDAP_USE_SSL: bool = False
    LDAP_USE_TLS: bool = False
    LDAP_BIND_DN: str = ""
    LDAP_BIND_PASSWORD: str = ""
    LDAP_AUTH_METHOD: str = "SIMPLE"  # SIMPLE, NTLM
    LDAP_BASE_DN: str = "dc=example,dc=com"
    LDAP_USER_SEARCH_BASE: str = "ou=users,dc=example,dc=com"
    LDAP_GROUP_SEARCH_BASE: str = "ou=groups,dc=example,dc=com"
    LDAP_USER_FILTER: str = "(uid={username})"
    LDAP_USERNAME_ATTR: str = "uid"
    LDAP_EMAIL_ATTR: str = "mail"
    LDAP_FIRST_NAME_ATTR: str = "givenName"
    LDAP_LAST_NAME_ATTR: str = "sn"
    LDAP_DISPLAY_NAME_ATTR: str = "displayName"
    LDAP_GROUP_FILTER: str = "(member={user_dn})"
    LDAP_GROUP_NAME_ATTR: str = "cn"
    LDAP_ADMIN_GROUPS: List[str] = ["administrators", "admin"]
    LDAP_USER_GROUPS: List[str] = ["users"]
    LDAP_AUTO_CREATE_USERS: bool = True
    LDAP_AUTO_UPDATE_USERS: bool = True
    LDAP_SYNC_GROUPS: bool = True

    # SSO配置
    SSO_ENABLED: bool = False
    SSO_TYPE: str = "saml"  # saml, oauth, oidc

    # SAML配置
    SAML_IDP_URL: str = ""
    SAML_IDP_CERT: str = ""
    SAML_SP_ENTITY_ID: str = "smart-aps"
    SAML_ACS_URL: str = "http://localhost:8000/api/v1/auth/saml/acs"
    SAML_SLS_URL: str = "http://localhost:8000/api/v1/auth/saml/sls"

    # OAuth/OIDC配置
    OAUTH_CLIENT_ID: str = ""
    OAUTH_CLIENT_SECRET: str = ""
    OAUTH_AUTH_URL: str = ""
    OAUTH_TOKEN_URL: str = ""
    OAUTH_USERINFO_URL: str = ""
    OAUTH_REDIRECT_URI: str = "http://localhost:8000/api/v1/auth/oauth/callback"
    OAUTH_SCOPE: str = "openid profile email"

    # SSO用户属性映射
    SSO_USERNAME_CLAIM: str = "sub"
    SSO_EMAIL_CLAIM: str = "email"
    SSO_NAME_CLAIM: str = "name"
    SSO_GROUPS_CLAIM: str = "groups"
    SSO_ADMIN_GROUPS: List[str] = ["administrators", "admin"]
    SSO_USER_GROUPS: List[str] = ["users"]
    SSO_AUTO_CREATE_USERS: bool = True
    SSO_AUTO_UPDATE_USERS: bool = True

    # 算法配置
    MILP_SOLVER_TIMEOUT: int = 1800  # 30分钟
    MILP_GAP_TOLERANCE: float = 0.01
    MAX_CONCURRENT_CALCULATIONS: int = 2

    @validator("DATABASE_URL", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: dict) -> str:
        """构建主数据库连接字符串 (MySQL)"""
        if isinstance(v, str):
            return v
        return (
            f"mysql+aiomysql://{values.get('DB_USER')}:{values.get('DB_PASSWORD')}"
            f"@{values.get('DB_HOST')}:{values.get('DB_PORT')}/{values.get('DB_NAME')}"
            f"?charset=utf8mb4"
        )

    @validator("INVENTORY_DB_URL", pre=True)
    def assemble_inventory_db_connection(cls, v: Optional[str], values: dict) -> str:
        """构建库存数据库连接字符串"""
        if isinstance(v, str):
            return v
        return (
            f"mysql+aiomysql://{values.get('INVENTORY_DB_USER')}:{values.get('INVENTORY_DB_PASSWORD')}"
            f"@{values.get('INVENTORY_DB_HOST')}:{values.get('INVENTORY_DB_PORT')}/{values.get('INVENTORY_DB_NAME')}"
            f"?charset=utf8mb4"
        )

    @validator("PRODUCTION_ANALYSIS_DB_URL", pre=True)
    def assemble_production_analysis_db_connection(cls, v: Optional[str], values: dict) -> str:
        """构建生产数据分析数据库连接字符串"""
        if isinstance(v, str):
            return v
        return (
            f"mysql+aiomysql://{values.get('PRODUCTION_ANALYSIS_DB_USER')}:{values.get('PRODUCTION_ANALYSIS_DB_PASSWORD')}"
            f"@{values.get('PRODUCTION_ANALYSIS_DB_HOST')}:{values.get('PRODUCTION_ANALYSIS_DB_PORT')}/{values.get('PRODUCTION_ANALYSIS_DB_NAME')}"
            f"?charset=utf8mb4"
        )

    @validator("ERP_DB_URL", pre=True)
    def assemble_erp_db_connection(cls, v: Optional[str], values: dict) -> str:
        """构建ERP数据库连接字符串"""
        if isinstance(v, str):
            return v
        return (
            f"mysql+aiomysql://{values.get('ERP_DB_USER')}:{values.get('ERP_DB_PASSWORD')}"
            f"@{values.get('ERP_DB_HOST')}:{values.get('ERP_DB_PORT')}/{values.get('ERP_DB_NAME')}"
            f"?charset=utf8mb4"
        )

    @validator("MES_DB_URL", pre=True)
    def assemble_mes_db_connection(cls, v: Optional[str], values: dict) -> str:
        """构建MES数据库连接字符串"""
        if isinstance(v, str):
            return v
        return (
            f"mysql+aiomysql://{values.get('MES_DB_USER')}:{values.get('MES_DB_PASSWORD')}"
            f"@{values.get('MES_DB_HOST')}:{values.get('MES_DB_PORT')}/{values.get('MES_DB_NAME')}"
            f"?charset=utf8mb4"
        )

    @validator("REDIS_URL", pre=True)
    def assemble_redis_connection(cls, v: Optional[str], values: dict) -> str:
        """构建Redis连接字符串"""
        if isinstance(v, str):
            return v

        password_part = ""
        if values.get("REDIS_PASSWORD"):
            password_part = f":{values.get('REDIS_PASSWORD')}@"

        return (
            f"redis://{password_part}{values.get('REDIS_HOST')}"
            f":{values.get('REDIS_PORT')}/{values.get('REDIS_DB')}"
        )

    @validator("ENCRYPTION_KEY", pre=True)
    def generate_encryption_key(cls, v: Optional[str]) -> str:
        """生成加密密钥"""
        if v:
            return v

        # 在生产环境中应该从安全的地方获取密钥
        from cryptography.fernet import Fernet
        return Fernet.generate_key().decode()

    def get_current_timestamp(self) -> str:
        """获取当前时间戳"""
        return datetime.utcnow().isoformat() + "Z"

    def is_development(self) -> bool:
        """是否为开发环境"""
        return self.DEBUG

    def is_production(self) -> bool:
        """是否为生产环境"""
        return not self.DEBUG

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True


@lru_cache()
def get_settings() -> Settings:
    """获取配置实例（单例模式）"""
    return Settings()


# 全局配置实例
settings = get_settings()


# 创建必要的目录
def create_directories():
    """创建必要的目录"""
    directories = [
        settings.UPLOAD_DIR,
        "logs",
        "temp",
        "exports"
    ]

    for directory in directories:
        os.makedirs(directory, exist_ok=True)


# 初始化时创建目录
create_directories()
