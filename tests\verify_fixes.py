"""
验证修复功能的简单测试脚本
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

def test_database_config():
    """测试数据库配置"""
    print("🔍 测试数据库配置...")
    
    try:
        # 测试后端配置
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))
        from app.core.config import Settings
        
        settings = Settings()
        
        # 检查MySQL端口
        assert settings.DB_PORT == 3306, f"数据库端口错误: {settings.DB_PORT}, 应该是3306"
        
        # 检查连接字符串生成
        values = {
            'DB_USER': 'smart_aps',
            'DB_PASSWORD': 'password',
            'DB_HOST': 'localhost',
            'DB_PORT': 3306,
            'DB_NAME': 'smart_aps'
        }
        
        db_url = settings.assemble_db_connection(None, values)
        assert "mysql+aiomysql" in db_url, f"连接字符串错误: {db_url}"
        assert "3306" in db_url, f"端口未包含在连接字符串中: {db_url}"
        assert "charset=utf8mb4" in db_url, f"字符集未包含: {db_url}"
        
        print("✅ 数据库配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 数据库配置测试失败: {str(e)}")
        return False


def test_email_extraction_functions():
    """测试邮件提取功能"""
    print("🔍 测试邮件提取功能...")
    
    try:
        # 添加前端路径
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'frontend'))
        
        # 测试最新回复提取
        from utils.file_utils import _extract_latest_reply, _clean_email_content
        
        # 测试Outlook分隔符
        email_body = """
最新回复内容：
产品A  100  2024-01-15

-----Original Message-----
From: <EMAIL>
原始邮件内容...
"""
        
        result = _extract_latest_reply(email_body)
        assert "最新回复内容" in result, "未正确提取最新回复"
        assert "Original Message" not in result, "包含了原始邮件内容"
        
        # 测试Gmail分隔符
        gmail_body = """
新的计划：
产品B  200

On Mon, Jan 1, 2024 at 10:00 AM <EMAIL> wrote:
> 之前的内容
"""
        
        result2 = _extract_latest_reply(gmail_body)
        assert "新的计划" in result2, "Gmail格式提取失败"
        assert "wrote:" not in result2, "包含了Gmail分隔符"
        
        # 测试内容清理
        dirty_content = """
From: <EMAIL>
To: <EMAIL>

实际内容
产品数据

--
Best regards,
John
"""
        
        cleaned = _clean_email_content(dirty_content)
        assert "From:" not in cleaned, "未清理邮件头"
        assert "Best regards" not in cleaned, "未清理签名"
        assert "实际内容" in cleaned, "清理过度"
        
        print("✅ 邮件提取功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 邮件提取功能测试失败: {str(e)}")
        return False


def test_table_extraction():
    """测试表格提取功能"""
    print("🔍 测试表格提取功能...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'frontend'))
        from utils.file_utils import (
            _extract_html_tables_from_text,
            _extract_text_tables_from_text,
            _extract_csv_tables_from_text
        )
        
        # 测试HTML表格提取
        html_text = """
<table>
<tr><th>产品</th><th>数量</th></tr>
<tr><td>产品A</td><td>100</td></tr>
</table>
"""
        
        html_result = _extract_html_tables_from_text(html_text)
        assert len(html_result) > 0, "HTML表格提取失败"
        assert "产品" in html_result[0]["columns"], "HTML表格列名提取失败"
        
        # 测试文本表格提取
        text_table = """
产品名称	数量	交期
产品A	100	2024-01-15
产品B	200	2024-01-20
"""
        
        text_result = _extract_text_tables_from_text(text_table)
        assert len(text_result) > 0, "文本表格提取失败"
        assert len(text_result[0]["data"]) == 2, "文本表格数据行数错误"
        
        # 测试CSV表格提取
        csv_text = """
产品名称,数量,交期
产品A,100,2024-01-15
产品B,200,2024-01-20
"""
        
        csv_result = _extract_csv_tables_from_text(csv_text)
        assert len(csv_result) > 0, "CSV表格提取失败"
        assert csv_result[0]["data"][0]["产品名称"] == "产品A", "CSV数据提取错误"
        
        print("✅ 表格提取功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 表格提取功能测试失败: {str(e)}")
        return False


def test_file_structure():
    """测试文件结构完整性"""
    print("🔍 测试文件结构完整性...")
    
    try:
        # 检查关键文件是否存在
        key_files = [
            "../backend/app/core/config.py",
            "../backend/app/core/database.py", 
            "../frontend/utils/file_utils.py",
            "../.env.example",
            "../README.md"
        ]
        
        for file_path in key_files:
            full_path = os.path.join(os.path.dirname(__file__), file_path)
            assert os.path.exists(full_path), f"关键文件不存在: {file_path}"
        
        # 检查.env.example中的MySQL配置
        env_path = os.path.join(os.path.dirname(__file__), "../.env.example")
        with open(env_path, 'r', encoding='utf-8') as f:
            env_content = f.read()
            assert "mysql+aiomysql" in env_content, ".env.example中MySQL配置错误"
            assert "3306" in env_content, ".env.example中端口配置错误"
        
        print("✅ 文件结构完整性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件结构完整性测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始验证修复功能...\n")
    
    tests = [
        ("数据库配置", test_database_config),
        ("邮件提取功能", test_email_extraction_functions),
        ("表格提取功能", test_table_extraction),
        ("文件结构完整性", test_file_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        if test_func():
            passed += 1
        print("-" * 50)
    
    print(f"\n🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复功能正常工作。")
        return True
    else:
        print("⚠️  部分测试失败，请检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
