# Smart APS 系统环境配置文件
# 复制此文件为 .env 并根据实际环境修改配置

# =============================================================================
# 基础配置
# =============================================================================
PROJECT_NAME=Smart APS
VERSION=1.0.0
DEBUG=false
HOST=0.0.0.0
PORT=8000

# =============================================================================
# 数据库配置 (PostgreSQL)
# =============================================================================
# 完整数据库URL（优先使用）
DATABASE_URL=postgresql+asyncpg://smart_aps:smart_aps_password@localhost:5432/smart_aps

# 或者分别配置各项（如果没有设置DATABASE_URL）
DB_HOST=localhost
DB_PORT=5432
DB_USER=smart_aps
DB_PASSWORD=smart_aps_password
DB_NAME=smart_aps

# =============================================================================
# Redis配置
# =============================================================================
# 完整Redis URL（优先使用）
REDIS_URL=redis://localhost:6379/0

# 或者分别配置各项
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# =============================================================================
# 安全配置
# =============================================================================
# JWT密钥（生产环境必须修改）
SECRET_KEY=your-super-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# 加密密钥（自动生成，可选）
ENCRYPTION_KEY=

# 限流配置
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# =============================================================================
# 文件上传配置
# =============================================================================
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=209715200
# 允许的文件类型（MIME类型，逗号分隔）
ALLOWED_FILE_TYPES=application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel,text/csv,message/rfc822,application/vnd.ms-outlook

# =============================================================================
# LLM服务配置
# =============================================================================
# 默认使用的LLM服务 (ollama 或 azure)
DEFAULT_LLM_SERVICE=ollama

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
OLLAMA_TIMEOUT=60

# Azure OpenAI配置
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key
AZURE_OPENAI_API_VERSION=2023-12-01-preview
AZURE_OPENAI_DEPLOYMENT_NAME=gpt-4

# =============================================================================
# 缓存配置
# =============================================================================
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# =============================================================================
# 日志配置
# =============================================================================
LOG_LEVEL=INFO
LOG_FILE=logs/smart_aps.log
LOG_MAX_SIZE=10485760
LOG_BACKUP_COUNT=5

# =============================================================================
# LDAP认证配置（可选）
# =============================================================================
LDAP_ENABLED=false
LDAP_SERVER_URI=ldap://localhost:389
LDAP_SERVER_PORT=389
LDAP_USE_SSL=false
LDAP_USE_TLS=false
LDAP_BIND_DN=cn=admin,dc=example,dc=com
LDAP_BIND_PASSWORD=admin_password
LDAP_AUTH_METHOD=SIMPLE
LDAP_BASE_DN=dc=example,dc=com
LDAP_USER_SEARCH_BASE=ou=users,dc=example,dc=com
LDAP_GROUP_SEARCH_BASE=ou=groups,dc=example,dc=com
LDAP_USER_FILTER=(uid={username})
LDAP_USERNAME_ATTR=uid
LDAP_EMAIL_ATTR=mail
LDAP_FIRST_NAME_ATTR=givenName
LDAP_LAST_NAME_ATTR=sn
LDAP_DISPLAY_NAME_ATTR=displayName
LDAP_GROUP_FILTER=(member={user_dn})
LDAP_GROUP_NAME_ATTR=cn
LDAP_ADMIN_GROUPS=administrators,admin
LDAP_USER_GROUPS=users
LDAP_AUTO_CREATE_USERS=true
LDAP_AUTO_UPDATE_USERS=true
LDAP_SYNC_GROUPS=true

# =============================================================================
# SSO配置（可选）
# =============================================================================
SSO_ENABLED=false
SSO_TYPE=saml

# SAML配置
SAML_IDP_URL=https://your-idp.com/saml/sso
SAML_IDP_CERT=-----BEGIN CERTIFICATE-----...-----END CERTIFICATE-----
SAML_SP_ENTITY_ID=smart-aps
SAML_ACS_URL=http://localhost:8000/api/v1/auth/saml/acs
SAML_SLS_URL=http://localhost:8000/api/v1/auth/saml/sls

# OAuth/OIDC配置
OAUTH_CLIENT_ID=your-client-id
OAUTH_CLIENT_SECRET=your-client-secret
OAUTH_AUTH_URL=https://your-provider.com/oauth/authorize
OAUTH_TOKEN_URL=https://your-provider.com/oauth/token
OAUTH_USERINFO_URL=https://your-provider.com/oauth/userinfo
OAUTH_REDIRECT_URI=http://localhost:8000/api/v1/auth/oauth/callback
OAUTH_SCOPE=openid profile email

# SSO用户属性映射
SSO_USERNAME_CLAIM=sub
SSO_EMAIL_CLAIM=email
SSO_NAME_CLAIM=name
SSO_GROUPS_CLAIM=groups
SSO_ADMIN_GROUPS=administrators,admin
SSO_USER_GROUPS=users
SSO_AUTO_CREATE_USERS=true
SSO_AUTO_UPDATE_USERS=true

# =============================================================================
# 算法配置
# =============================================================================
MILP_SOLVER_TIMEOUT=1800
MILP_GAP_TOLERANCE=0.01
MAX_CONCURRENT_CALCULATIONS=2

# =============================================================================
# API配置
# =============================================================================
API_V1_STR=/api/v1
ALLOWED_HOSTS=*

# =============================================================================
# 前端配置（Streamlit）
# =============================================================================
# Streamlit服务器配置
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=0.0.0.0
STREAMLIT_SERVER_HEADLESS=true
STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# =============================================================================
# 开发环境配置
# =============================================================================
# 开发环境特定配置
DEV_RELOAD=true
DEV_LOG_LEVEL=DEBUG

# =============================================================================
# 生产环境配置
# =============================================================================
# 生产环境特定配置
PROD_WORKERS=4
PROD_MAX_WORKERS=8
PROD_TIMEOUT=30

# =============================================================================
# 监控配置
# =============================================================================
# 健康检查配置
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# 指标收集
METRICS_ENABLED=true
METRICS_PORT=9090

# =============================================================================
# 备份配置
# =============================================================================
# 数据备份配置
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=backups

# =============================================================================
# 邮件配置（用于通知）
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false
EMAIL_FROM=Smart APS <<EMAIL>>

# =============================================================================
# 国际化配置
# =============================================================================
DEFAULT_LANGUAGE=zh
SUPPORTED_LANGUAGES=zh,en
TIMEZONE=Asia/Shanghai

# =============================================================================
# 扩展配置
# =============================================================================
# 插件配置
PLUGINS_ENABLED=true
PLUGINS_DIR=plugins
PLUGINS_AUTO_LOAD=true

# 自定义配置
CUSTOM_CONFIG_DIR=config
CUSTOM_TEMPLATES_DIR=templates
