"""
现代化导航组件
提供清新简洁的导航界面
"""

import streamlit as st
from typing import Dict, List, Any, Optional
from datetime import datetime
import json

class ModernNavigation:
    """现代化导航组件"""

    @staticmethod
    def create_breadcrumb(current_page: str, page_hierarchy: Dict[str, str] = None):
        """创建面包屑导航"""
        if page_hierarchy is None:
            page_hierarchy = {
                "01_综合仪表板": "首页 > 综合仪表板",
                "02_数据上传": "首页 > 数据管理 > 数据上传",
                "03_生产规划": "首页 > 生产管理 > 生产规划",
                "04_设备管理": "首页 > 设备管理",
                "05_计划监控": "首页 > 生产管理 > 计划监控",
                "06_数据分析": "首页 > 数据管理 > 数据分析",
                "07_智能助手": "首页 > AI助手",
                "08_PCI管理": "首页 > 专业管理 > PCI管理",
                "09_供应链协同": "首页 > 供应链管理",
                "10_能耗优化": "首页 > 优化管理 > 能耗优化",
                "11_算法中心": "首页 > 算法中心",
                "12_数据中心": "首页 > 数据中心",
                "13_系统管理": "首页 > 系统管理"
            }

        breadcrumb = page_hierarchy.get(current_page, "首页")

        st.markdown(f"""
        <div style="
            background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 8px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            border-left: 4px solid #4A90E2;
        ">
            <span style="color: #6c757d; font-size: 14px;">📍 {breadcrumb}</span>
        </div>
        """, unsafe_allow_html=True)

    @staticmethod
    def create_quick_actions(current_page: str):
        """创建智能快速操作按钮"""
        # 扩展的智能快速操作配置
        quick_actions = {
            "01_综合仪表板": [
                ("📊", "数据分析", "06_数据分析"),
                ("🏭", "生产规划", "03_生产规划"),
                ("🤖", "智能助手", "07_智能助手")
            ],
            "02_数据上传": [
                ("📊", "数据分析", "06_数据分析"),
                ("🏭", "生产规划", "03_生产规划"),
                ("📋", "综合仪表板", "01_综合仪表板")
            ],
            "03_生产规划": [
                ("📊", "计划监控", "05_计划监控"),
                ("⚙️", "设备管理", "04_设备管理"),
                ("🧮", "算法中心", "11_算法中心")
            ],
            "04_设备管理": [
                ("📊", "计划监控", "05_计划监控"),
                ("🏭", "生产规划", "03_生产规划"),
                ("📈", "数据分析", "06_数据分析")
            ],
            "05_计划监控": [
                ("🏭", "生产规划", "03_生产规划"),
                ("⚙️", "设备管理", "04_设备管理"),
                ("📊", "数据分析", "06_数据分析")
            ],
            "06_数据分析": [
                ("📋", "综合仪表板", "01_综合仪表板"),
                ("🏭", "生产规划", "03_生产规划"),
                ("🤖", "智能助手", "07_智能助手")
            ],
            "07_智能助手": [
                ("📊", "数据分析", "06_数据分析"),
                ("🧮", "算法中心", "11_算法中心"),
                ("📋", "综合仪表板", "01_综合仪表板")
            ],
            "08_PCI管理": [
                ("📊", "数据分析", "06_数据分析"),
                ("📁", "数据上传", "02_数据上传"),
                ("💾", "数据中心", "12_数据中心")
            ],
            "09_供应链协同": [
                ("🏭", "生产规划", "03_生产规划"),
                ("📊", "数据分析", "06_数据分析"),
                ("⚙️", "设备管理", "04_设备管理")
            ],
            "10_能耗优化": [
                ("⚙️", "设备管理", "04_设备管理"),
                ("📊", "数据分析", "06_数据分析"),
                ("🧮", "算法中心", "11_算法中心")
            ],
            "11_算法中心": [
                ("🏭", "生产规划", "03_生产规划"),
                ("📊", "数据分析", "06_数据分析"),
                ("🤖", "智能助手", "07_智能助手")
            ],
            "12_数据中心": [
                ("📁", "数据上传", "02_数据上传"),
                ("📊", "数据分析", "06_数据分析"),
                ("📋", "PCI管理", "08_PCI管理")
            ],
            "13_系统管理": [
                ("📋", "综合仪表板", "01_综合仪表板"),
                ("👥", "用户管理", "07_用户管理"),
                ("⚙️", "系统配置", "08_系统配置")
            ]
        }

        actions = quick_actions.get(current_page, [])
        if actions:
            st.markdown("### 🚀 智能推荐操作")
            cols = st.columns(len(actions))

            for i, (icon, name, page) in enumerate(actions):
                with cols[i]:
                    if st.button(f"{icon} {name}", key=f"quick_{page}", use_container_width=True):
                        # 记录用户行为
                        ModernNavigation.track_page_visit(page)
                        st.switch_page(f"pages/{page}.py")

    @staticmethod
    def track_page_visit(page_name: str):
        """记录页面访问历史"""
        if 'page_history' not in st.session_state:
            st.session_state.page_history = []

        # 避免重复记录相同页面
        if not st.session_state.page_history or st.session_state.page_history[-1] != page_name:
            st.session_state.page_history.append(page_name)

        # 只保留最近10个访问记录
        if len(st.session_state.page_history) > 10:
            st.session_state.page_history = st.session_state.page_history[-10:]

    @staticmethod
    def create_recent_pages_sidebar():
        """在侧边栏显示智能最近访问页面"""
        if 'page_history' in st.session_state and len(st.session_state.page_history) > 1:
            st.sidebar.markdown("### 📚 最近访问")

            # 获取最近访问的页面（排除当前页面）
            recent_pages = list(reversed(st.session_state.page_history[:-1]))[:5]

            # 统一的页面名称映射（与现有架构保持一致）
            page_names = {
                "01_综合仪表板": "📋 综合仪表板",
                "02_数据上传": "📁 数据上传",
                "03_生产规划": "🏭 生产规划",
                "04_设备管理": "⚙️ 设备管理",
                "05_计划监控": "📊 计划监控",
                "06_数据分析": "📈 数据分析",
                "07_智能助手": "🤖 智能助手",
                "08_PCI管理": "📋 PCI管理",
                "09_供应链协同": "🔗 供应链协同",
                "10_能耗优化": "⚡ 能耗优化",
                "11_算法中心": "🧮 算法中心",
                "12_数据中心": "💾 数据中心",
                "13_系统管理": "⚙️ 系统管理"
            }

            # 显示最近访问的页面，带访问频率提示
            page_frequency = {}
            for page in st.session_state.page_history:
                page_frequency[page] = page_frequency.get(page, 0) + 1

            for page in recent_pages:
                page_display = page_names.get(page, page)
                frequency = page_frequency.get(page, 1)

                # 根据访问频率添加热度指示
                if frequency >= 5:
                    heat_indicator = "🔥"
                elif frequency >= 3:
                    heat_indicator = "⭐"
                else:
                    heat_indicator = ""

                button_text = f"{page_display} {heat_indicator}".strip()

                if st.sidebar.button(button_text, key=f"recent_{page}", use_container_width=True):
                    ModernNavigation.track_page_visit(page)
                    st.switch_page(f"pages/{page}.py")

            # 显示访问统计
            if len(st.session_state.page_history) > 5:
                st.sidebar.markdown(f"*总访问次数: {len(st.session_state.page_history)}*")

    @staticmethod
    def create_smart_navigation_widget():
        """创建智能导航小部件"""
        with st.sidebar:
            st.markdown("### 🧭 智能导航")

            # 工作流建议
            if 'page_history' in st.session_state and len(st.session_state.page_history) >= 3:
                # 分析常用的页面序列
                recent_sequence = st.session_state.page_history[-3:]

                # 预定义的工作流模式
                workflow_patterns = {
                    ("02_数据上传", "06_数据分析", "03_生产规划"): {
                        "name": "📊 数据分析工作流",
                        "next_suggestions": ["05_计划监控", "04_设备管理"]
                    },
                    ("01_综合仪表板", "04_设备管理", "05_计划监控"): {
                        "name": "🏭 生产监控工作流",
                        "next_suggestions": ["06_数据分析", "07_智能助手"]
                    },
                    ("07_智能助手", "11_算法中心", "03_生产规划"): {
                        "name": "🤖 智能规划工作流",
                        "next_suggestions": ["05_计划监控", "06_数据分析"]
                    }
                }

                # 检查是否匹配已知工作流
                for pattern, workflow in workflow_patterns.items():
                    if tuple(recent_sequence) == pattern:
                        st.sidebar.success(f"检测到: {workflow['name']}")
                        st.sidebar.markdown("**建议下一步:**")

                        for suggestion in workflow['next_suggestions']:
                            # 使用统一的页面名称映射
                            page_names = {
                                "05_计划监控": "📊 计划监控",
                                "04_设备管理": "⚙️ 设备管理",
                                "06_数据分析": "📈 数据分析",
                                "07_智能助手": "🤖 智能助手"
                            }

                            suggestion_name = page_names.get(suggestion, suggestion)
                            if st.sidebar.button(
                                suggestion_name,
                                key=f"workflow_{suggestion}",
                                use_container_width=True
                            ):
                                ModernNavigation.track_page_visit(suggestion)
                                st.switch_page(f"pages/{suggestion}.py")
                        break

    # 导航菜单配置
    NAVIGATION_ITEMS = [
        {
            "title": "综合仪表板",
            "icon": "📊",
            "page": "pages/01_综合仪表板.py",
            "description": "实时监控生产状态",
            "category": "core"
        },
        {
            "title": "数据上传",
            "icon": "📁",
            "page": "pages/02_数据上传.py",
            "description": "上传和管理数据文件",
            "category": "core"
        },
        {
            "title": "生产规划",
            "icon": "📋",
            "page": "pages/03_生产规划.py",
            "description": "制定生产计划",
            "category": "core"
        },
        {
            "title": "设备管理",
            "icon": "🏭",
            "page": "pages/04_设备管理.py",
            "description": "管理生产设备",
            "category": "core"
        },
        {
            "title": "计划监控",
            "icon": "📈",
            "page": "pages/05_计划监控.py",
            "description": "监控计划执行",
            "category": "core"
        },
        {
            "title": "数据分析",
            "icon": "📊",
            "page": "pages/06_数据分析.py",
            "description": "分析生产数据",
            "category": "core"
        },
        {
            "title": "智能助手",
            "icon": "🤖",
            "page": "pages/07_智能助手.py",
            "description": "AI智能助手",
            "category": "ai"
        },
        {
            "title": "PCI管理",
            "icon": "🔬",
            "page": "pages/08_PCI管理.py",
            "description": "PCI数据管理",
            "category": "advanced"
        },
        {
            "title": "供应链协同",
            "icon": "🌐",
            "page": "pages/09_供应链协同.py",
            "description": "供应链管理",
            "category": "advanced"
        },
        {
            "title": "能耗优化",
            "icon": "⚡",
            "page": "pages/10_能耗优化.py",
            "description": "能源管理优化",
            "category": "advanced"
        },
        {
            "title": "算法中心",
            "icon": "🧮",
            "page": "pages/11_算法中心.py",
            "description": "算法管理中心",
            "category": "system"
        },
        {
            "title": "数据中心",
            "icon": "📊",
            "page": "pages/12_数据中心.py",
            "description": "数据管理中心",
            "category": "system"
        },
        {
            "title": "系统管理",
            "icon": "👥",
            "page": "pages/13_系统管理.py",
            "description": "系统配置管理",
            "category": "system"
        }
    ]

    @staticmethod
    def render_sidebar_navigation():
        """渲染侧边栏导航"""
        with st.sidebar:
            # Logo和标题区域
            st.markdown("""
            <div style="
                text-align: center;
                padding: 1rem 0;
                margin-bottom: 1rem;
                border-bottom: 1px solid #E8F2FF;
            ">
                <div style="
                    font-size: 1.5rem;
                    font-weight: 600;
                    background: linear-gradient(135deg, #4A90E2 0%, #50C878 100%);
                    -webkit-background-clip: text;
                    -webkit-text-fill-color: transparent;
                    background-clip: text;
                ">
                    📊 Smart APS
                </div>
                <div style="
                    font-size: 0.8rem;
                    color: #5A6C7D;
                    margin-top: 0.2rem;
                ">
                    智能生产管理系统
                </div>
            </div>
            """, unsafe_allow_html=True)

            # 用户信息
            user_info = st.session_state.get('user_info', {})
            user_name = user_info.get('full_name', '用户')

            st.markdown(f"""
            <div style="
                background: linear-gradient(135deg, #F8FAFC 0%, #E8F2FF 100%);
                padding: 1rem;
                border-radius: 8px;
                margin-bottom: 1rem;
                text-align: center;
            ">
                <div style="
                    font-size: 0.9rem;
                    color: #2C3E50;
                    font-weight: 500;
                ">
                    👋 欢迎，{user_name}
                </div>
            </div>
            """, unsafe_allow_html=True)

            # 导航菜单
            ModernNavigation._render_navigation_menu()

            # 底部操作
            st.markdown("---")
            if st.button("🚪 退出登录", use_container_width=True, type="secondary"):
                for key in list(st.session_state.keys()):
                    del st.session_state[key]
                st.rerun()

    @staticmethod
    def _render_navigation_menu():
        """渲染导航菜单"""
        # 按类别分组
        categories = {
            "core": {"title": "🏠 核心功能", "items": []},
            "ai": {"title": "🤖 智能功能", "items": []},
            "advanced": {"title": "🚀 高级功能", "items": []},
            "system": {"title": "⚙️ 系统管理", "items": []}
        }

        # 分类导航项
        for item in ModernNavigation.NAVIGATION_ITEMS:
            category = item.get("category", "core")
            if category in categories:
                categories[category]["items"].append(item)

        # 渲染各类别
        for category_key, category_data in categories.items():
            if not category_data["items"]:
                continue

            st.markdown(f"**{category_data['title']}**")

            for item in category_data["items"]:
                # 创建导航按钮
                button_html = f"""
                <div style="margin-bottom: 0.5rem;">
                    <div style="
                        display: flex;
                        align-items: center;
                        padding: 0.75rem;
                        background: white;
                        border: 1px solid #E8F2FF;
                        border-radius: 8px;
                        cursor: pointer;
                        transition: all 0.3s ease;
                        text-decoration: none;
                    " onmouseover="this.style.background='#F8FAFC'; this.style.borderColor='#4A90E2';"
                       onmouseout="this.style.background='white'; this.style.borderColor='#E8F2FF';">
                        <span style="font-size: 1.2rem; margin-right: 0.75rem;">{item['icon']}</span>
                        <div style="flex: 1;">
                            <div style="
                                font-weight: 500;
                                color: #2C3E50;
                                font-size: 0.9rem;
                                margin-bottom: 0.2rem;
                            ">{item['title']}</div>
                            <div style="
                                font-size: 0.7rem;
                                color: #5A6C7D;
                            ">{item['description']}</div>
                        </div>
                    </div>
                </div>
                """

                # 使用按钮进行页面跳转
                if st.button(
                    f"{item['icon']} {item['title']}",
                    use_container_width=True,
                    key=f"nav_{item['title']}",
                    help=item['description']
                ):
                    st.switch_page(item['page'])

            st.markdown("")  # 添加间距

    @staticmethod
    def render_breadcrumb(current_page: str, pages: List[Dict[str, str]] = None):
        """渲染面包屑导航"""
        if pages is None:
            pages = [{"title": "首页", "url": "main.py"}]

        breadcrumb_html = """
        <div style="
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            margin-bottom: 1rem;
            font-size: 0.9rem;
            color: #5A6C7D;
        ">
        """

        for i, page in enumerate(pages):
            if i > 0:
                breadcrumb_html += """
                <span style="margin: 0 0.5rem; color: #8B9DC3;">></span>
                """

            if i == len(pages) - 1:  # 当前页面
                breadcrumb_html += f"""
                <span style="color: #4A90E2; font-weight: 500;">{current_page}</span>
                """
            else:
                breadcrumb_html += f"""
                <a href="{page['url']}" style="
                    color: #5A6C7D;
                    text-decoration: none;
                " onmouseover="this.style.color='#4A90E2';"
                   onmouseout="this.style.color='#5A6C7D';">
                    {page['title']}
                </a>
                """

        breadcrumb_html += "</div>"

        st.markdown(breadcrumb_html, unsafe_allow_html=True)

    @staticmethod
    def render_quick_actions():
        """渲染快速操作面板"""
        st.markdown("### ⚡ 快速操作")

        quick_actions = [
            {"title": "数据上传", "icon": "📁", "page": "pages/02_数据上传.py"},
            {"title": "生成计划", "icon": "📋", "page": "pages/03_生产规划.py"},
            {"title": "查看报告", "icon": "📊", "page": "pages/06_数据分析.py"},
            {"title": "AI助手", "icon": "🤖", "page": "pages/07_智能助手.py"}
        ]

        cols = st.columns(len(quick_actions))

        for i, action in enumerate(quick_actions):
            with cols[i]:
                if st.button(
                    f"{action['icon']}\n{action['title']}",
                    use_container_width=True,
                    key=f"quick_{action['title']}"
                ):
                    st.switch_page(action['page'])

def apply_navigation_styles():
    """应用导航样式"""
    st.markdown("""
    <style>
    /* 侧边栏样式优化 */
    .css-1d391kg {
        background: linear-gradient(180deg, #FFFFFF 0%, #F8FAFC 100%);
        border-right: 1px solid #E8F2FF;
    }

    /* 导航按钮样式 */
    .stButton > button {
        width: 100%;
        text-align: left;
        background: white;
        border: 1px solid #E8F2FF;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }

    .stButton > button:hover {
        background: #F8FAFC;
        border-color: #4A90E2;
        transform: translateX(2px);
    }

    /* 隐藏Streamlit默认样式 */
    .css-1rs6os {
        background: transparent;
    }

    .css-17eq0hr {
        background: transparent;
    }
    </style>
    """, unsafe_allow_html=True)
