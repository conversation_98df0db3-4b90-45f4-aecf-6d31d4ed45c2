"""
邮件服务模块
提供邮件发送、模板管理、通知功能
"""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional, Dict, Any
from pathlib import Path
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor

from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务类"""
    
    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_tls = settings.SMTP_TLS
        self.smtp_ssl = settings.SMTP_SSL
        self.email_from = settings.EMAIL_FROM
        self.enabled = settings.EMAIL_ENABLED
        self.executor = ThreadPoolExecutor(max_workers=3)
    
    def _create_smtp_connection(self):
        """创建SMTP连接"""
        try:
            if self.smtp_ssl:
                server = smtplib.SMTP_SSL(self.smtp_host, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_host, self.smtp_port)
                if self.smtp_tls:
                    server.starttls()
            
            if self.smtp_user and self.smtp_password:
                server.login(self.smtp_user, self.smtp_password)
            
            return server
        except Exception as e:
            logger.error(f"创建SMTP连接失败: {str(e)}")
            raise
    
    def send_email(
        self,
        to_emails: List[str],
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[str]] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None
    ) -> bool:
        """发送邮件"""
        if not self.enabled:
            logger.warning("邮件功能未启用")
            return False
        
        if not to_emails:
            logger.warning("收件人列表为空")
            return False
        
        try:
            # 创建邮件消息
            msg = MIMEMultipart('alternative')
            msg['From'] = self.email_from
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject
            
            if cc_emails:
                msg['Cc'] = ', '.join(cc_emails)
            
            # 添加文本内容
            if body:
                text_part = MIMEText(body, 'plain', 'utf-8')
                msg.attach(text_part)
            
            # 添加HTML内容
            if html_body:
                html_part = MIMEText(html_body, 'html', 'utf-8')
                msg.attach(html_part)
            
            # 添加附件
            if attachments:
                for file_path in attachments:
                    if Path(file_path).exists():
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                            encoders.encode_base64(part)
                            part.add_header(
                                'Content-Disposition',
                                f'attachment; filename= {Path(file_path).name}'
                            )
                            msg.attach(part)
            
            # 发送邮件
            server = self._create_smtp_connection()
            
            # 收件人列表
            recipients = to_emails.copy()
            if cc_emails:
                recipients.extend(cc_emails)
            if bcc_emails:
                recipients.extend(bcc_emails)
            
            server.send_message(msg, to_addrs=recipients)
            server.quit()
            
            logger.info(f"邮件发送成功: {subject} to {to_emails}")
            return True
            
        except Exception as e:
            logger.error(f"邮件发送失败: {str(e)}")
            return False
    
    async def send_email_async(
        self,
        to_emails: List[str],
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[str]] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None
    ) -> bool:
        """异步发送邮件"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self.send_email,
            to_emails,
            subject,
            body,
            html_body,
            attachments,
            cc_emails,
            bcc_emails
        )
    
    def send_notification(
        self,
        notification_type: str,
        recipients: List[str],
        data: Dict[str, Any]
    ) -> bool:
        """发送通知邮件"""
        templates = {
            "anomaly_alert": {
                "subject": "🚨 Smart APS 异常警报",
                "template": self._get_anomaly_alert_template()
            },
            "report_ready": {
                "subject": "📊 Smart APS 报告已生成",
                "template": self._get_report_ready_template()
            },
            "system_alert": {
                "subject": "⚠️ Smart APS 系统警报",
                "template": self._get_system_alert_template()
            },
            "backup_complete": {
                "subject": "✅ Smart APS 备份完成",
                "template": self._get_backup_complete_template()
            }
        }
        
        template_info = templates.get(notification_type)
        if not template_info:
            logger.warning(f"未知的通知类型: {notification_type}")
            return False
        
        try:
            # 渲染模板
            subject = template_info["subject"]
            html_body = template_info["template"].format(**data)
            
            # 发送邮件
            return self.send_email(
                to_emails=recipients,
                subject=subject,
                body="",  # 纯文本版本可以为空，使用HTML版本
                html_body=html_body
            )
            
        except Exception as e:
            logger.error(f"发送通知邮件失败: {str(e)}")
            return False
    
    def _get_anomaly_alert_template(self) -> str:
        """异常警报邮件模板"""
        return """
        <html>
        <body>
            <h2>🚨 异常警报</h2>
            <p>检测到系统异常，请及时处理：</p>
            <ul>
                <li><strong>异常类型</strong>: {anomaly_type}</li>
                <li><strong>严重程度</strong>: {severity}</li>
                <li><strong>发生时间</strong>: {timestamp}</li>
                <li><strong>影响范围</strong>: {affected_area}</li>
            </ul>
            <p><strong>详细描述</strong>: {description}</p>
            <p><strong>建议措施</strong>: {recommended_action}</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """
    
    def _get_report_ready_template(self) -> str:
        """报告就绪邮件模板"""
        return """
        <html>
        <body>
            <h2>📊 报告已生成</h2>
            <p>您请求的报告已经生成完成：</p>
            <ul>
                <li><strong>报告名称</strong>: {report_name}</li>
                <li><strong>报告类型</strong>: {report_type}</li>
                <li><strong>生成时间</strong>: {generated_at}</li>
                <li><strong>数据范围</strong>: {data_range}</li>
            </ul>
            <p>请登录系统查看详细报告内容。</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """
    
    def _get_system_alert_template(self) -> str:
        """系统警报邮件模板"""
        return """
        <html>
        <body>
            <h2>⚠️ 系统警报</h2>
            <p>系统监控发现以下问题：</p>
            <ul>
                <li><strong>警报类型</strong>: {alert_type}</li>
                <li><strong>系统组件</strong>: {component}</li>
                <li><strong>当前状态</strong>: {status}</li>
                <li><strong>检测时间</strong>: {timestamp}</li>
            </ul>
            <p><strong>详细信息</strong>: {details}</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """
    
    def _get_backup_complete_template(self) -> str:
        """备份完成邮件模板"""
        return """
        <html>
        <body>
            <h2>✅ 备份完成</h2>
            <p>系统备份已成功完成：</p>
            <ul>
                <li><strong>备份类型</strong>: {backup_type}</li>
                <li><strong>备份大小</strong>: {backup_size}</li>
                <li><strong>备份时间</strong>: {backup_time}</li>
                <li><strong>存储位置</strong>: {storage_location}</li>
            </ul>
            <p>备份文件已安全存储，系统运行正常。</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """
    
    def test_connection(self) -> Dict[str, Any]:
        """测试邮件连接"""
        if not self.enabled:
            return {
                "success": False,
                "message": "邮件功能未启用"
            }
        
        try:
            server = self._create_smtp_connection()
            server.quit()
            return {
                "success": True,
                "message": "邮件连接测试成功"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"邮件连接测试失败: {str(e)}"
            }
    
    def update_config(self, config: Dict[str, Any]):
        """更新邮件配置"""
        if "smtp_host" in config:
            self.smtp_host = config["smtp_host"]
        if "smtp_port" in config:
            self.smtp_port = config["smtp_port"]
        if "smtp_user" in config:
            self.smtp_user = config["smtp_user"]
        if "smtp_password" in config:
            self.smtp_password = config["smtp_password"]
        if "smtp_tls" in config:
            self.smtp_tls = config["smtp_tls"]
        if "smtp_ssl" in config:
            self.smtp_ssl = config["smtp_ssl"]
        if "email_from" in config:
            self.email_from = config["email_from"]
        if "enabled" in config:
            self.enabled = config["enabled"]


# 全局邮件服务实例
email_service = EmailService()
