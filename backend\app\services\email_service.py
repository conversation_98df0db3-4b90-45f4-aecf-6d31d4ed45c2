"""
邮件服务模块
提供邮件发送、模板管理、通知功能
"""

import smtplib
import logging
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Optional, Dict, Any
from pathlib import Path
from datetime import datetime
import asyncio
from concurrent.futures import ThreadPoolExecutor

from app.core.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """邮件服务类"""

    def __init__(self):
        self.smtp_host = settings.SMTP_HOST
        self.smtp_port = settings.SMTP_PORT
        self.smtp_user = settings.SMTP_USER
        self.smtp_password = settings.SMTP_PASSWORD
        self.smtp_tls = settings.SMTP_TLS
        self.smtp_ssl = settings.SMTP_SSL
        self.email_from = settings.EMAIL_FROM
        self.enabled = settings.EMAIL_ENABLED
        self.executor = ThreadPoolExecutor(max_workers=3)

        # 预配置收件人列表
        self.admin_recipients = self._parse_recipients(settings.EMAIL_ADMIN_RECIPIENTS)
        self.alert_recipients = self._parse_recipients(settings.EMAIL_ALERT_RECIPIENTS)
        self.report_recipients = self._parse_recipients(settings.EMAIL_REPORT_RECIPIENTS)

    def _parse_recipients(self, recipients_str: str) -> List[str]:
        """解析收件人字符串为列表"""
        if not recipients_str:
            return []
        return [email.strip() for email in recipients_str.split(',') if email.strip()]

    def _create_smtp_connection(self):
        """创建SMTP连接"""
        try:
            if self.smtp_ssl:
                server = smtplib.SMTP_SSL(self.smtp_host, self.smtp_port)
            else:
                server = smtplib.SMTP(self.smtp_host, self.smtp_port)
                if self.smtp_tls:
                    server.starttls()

            if self.smtp_user and self.smtp_password:
                server.login(self.smtp_user, self.smtp_password)

            return server
        except Exception as e:
            logger.error(f"创建SMTP连接失败: {str(e)}")
            raise

    def send_email(
        self,
        to_emails: List[str],
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[str]] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None
    ) -> bool:
        """发送邮件"""
        if not self.enabled:
            logger.warning("邮件功能未启用")
            return False

        if not to_emails:
            logger.warning("收件人列表为空")
            return False

        try:
            # 创建邮件消息
            msg = MIMEMultipart('alternative')
            msg['From'] = self.email_from
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = subject

            if cc_emails:
                msg['Cc'] = ', '.join(cc_emails)

            # 添加文本内容
            if body:
                text_part = MIMEText(body, 'plain', 'utf-8')
                msg.attach(text_part)

            # 添加HTML内容
            if html_body:
                html_part = MIMEText(html_body, 'html', 'utf-8')
                msg.attach(html_part)

            # 添加附件
            if attachments:
                for file_path in attachments:
                    if Path(file_path).exists():
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                            encoders.encode_base64(part)
                            part.add_header(
                                'Content-Disposition',
                                f'attachment; filename= {Path(file_path).name}'
                            )
                            msg.attach(part)

            # 发送邮件
            server = self._create_smtp_connection()

            # 收件人列表
            recipients = to_emails.copy()
            if cc_emails:
                recipients.extend(cc_emails)
            if bcc_emails:
                recipients.extend(bcc_emails)

            server.send_message(msg, to_addrs=recipients)
            server.quit()

            logger.info(f"邮件发送成功: {subject} to {to_emails}")
            return True

        except Exception as e:
            logger.error(f"邮件发送失败: {str(e)}")
            return False

    async def send_email_async(
        self,
        to_emails: List[str],
        subject: str,
        body: str,
        html_body: Optional[str] = None,
        attachments: Optional[List[str]] = None,
        cc_emails: Optional[List[str]] = None,
        bcc_emails: Optional[List[str]] = None
    ) -> bool:
        """异步发送邮件"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self.send_email,
            to_emails,
            subject,
            body,
            html_body,
            attachments,
            cc_emails,
            bcc_emails
        )

    def send_notification(
        self,
        notification_type: str,
        recipients: Optional[List[str]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """发送通知邮件"""
        if data is None:
            data = {}

        # 如果没有指定收件人，使用预配置的收件人
        if not recipients:
            recipients = self._get_default_recipients(notification_type)

        if not recipients:
            logger.warning(f"通知类型 {notification_type} 没有配置收件人")
            return False

        templates = {
            "anomaly_alert": {
                "subject": "🚨 Smart APS 异常警报",
                "template": self._get_anomaly_alert_template()
            },
            "report_ready": {
                "subject": "📊 Smart APS 报告已生成",
                "template": self._get_report_ready_template()
            },
            "system_alert": {
                "subject": "⚠️ Smart APS 系统警报",
                "template": self._get_system_alert_template()
            },
            "backup_complete": {
                "subject": "✅ Smart APS 备份完成",
                "template": self._get_backup_complete_template()
            },
            "production_alert": {
                "subject": "🏭 Smart APS 生产警报",
                "template": self._get_production_alert_template()
            },
            "equipment_alert": {
                "subject": "⚙️ Smart APS 设备警报",
                "template": self._get_equipment_alert_template()
            }
        }

        template_info = templates.get(notification_type)
        if not template_info:
            logger.warning(f"未知的通知类型: {notification_type}")
            return False

        try:
            # 渲染模板
            subject = template_info["subject"]
            html_body = template_info["template"].format(**data)

            # 发送邮件
            return self.send_email(
                to_emails=recipients,
                subject=subject,
                body="",  # 纯文本版本可以为空，使用HTML版本
                html_body=html_body
            )

        except Exception as e:
            logger.error(f"发送通知邮件失败: {str(e)}")
            return False

    def _get_default_recipients(self, notification_type: str) -> List[str]:
        """获取默认收件人列表"""
        recipient_mapping = {
            "anomaly_alert": self.alert_recipients,
            "system_alert": self.alert_recipients,
            "equipment_alert": self.alert_recipients,
            "production_alert": self.alert_recipients,
            "report_ready": self.report_recipients,
            "backup_complete": self.admin_recipients
        }
        return recipient_mapping.get(notification_type, self.admin_recipients)

    def send_system_notification(
        self,
        title: str,
        message: str,
        severity: str = "info",
        recipients: Optional[List[str]] = None
    ) -> bool:
        """发送系统通知（简化接口）"""
        if not recipients:
            recipients = self.admin_recipients

        data = {
            "title": title,
            "message": message,
            "severity": severity,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "system": "Smart APS"
        }

        return self.send_notification("system_alert", recipients, data)

    def send_production_notification(
        self,
        production_line: str,
        issue_type: str,
        description: str,
        severity: str = "medium",
        recipients: Optional[List[str]] = None
    ) -> bool:
        """发送生产通知"""
        if not recipients:
            recipients = self.alert_recipients

        data = {
            "production_line": production_line,
            "issue_type": issue_type,
            "description": description,
            "severity": severity,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "recommended_action": self._get_recommended_action(issue_type)
        }

        return self.send_notification("production_alert", recipients, data)

    def send_equipment_notification(
        self,
        equipment_id: str,
        equipment_name: str,
        status: str,
        issue_description: str,
        recipients: Optional[List[str]] = None
    ) -> bool:
        """发送设备通知"""
        if not recipients:
            recipients = self.alert_recipients

        data = {
            "equipment_id": equipment_id,
            "equipment_name": equipment_name,
            "status": status,
            "issue_description": issue_description,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "maintenance_required": status in ["故障", "异常"]
        }

        return self.send_notification("equipment_alert", recipients, data)

    def _get_recommended_action(self, issue_type: str) -> str:
        """获取推荐处理措施"""
        actions = {
            "设备故障": "立即停机检查，联系维修人员",
            "质量异常": "检查生产参数，调整工艺设置",
            "产能不足": "检查设备状态，优化生产计划",
            "原料短缺": "联系采购部门，安排紧急补货",
            "人员不足": "调配人员，安排加班或临时工",
            "工艺异常": "检查工艺参数，联系工艺工程师"
        }
        return actions.get(issue_type, "请联系相关负责人处理")

    def _get_anomaly_alert_template(self) -> str:
        """异常警报邮件模板"""
        return """
        <html>
        <body>
            <h2>🚨 异常警报</h2>
            <p>检测到系统异常，请及时处理：</p>
            <ul>
                <li><strong>异常类型</strong>: {anomaly_type}</li>
                <li><strong>严重程度</strong>: {severity}</li>
                <li><strong>发生时间</strong>: {timestamp}</li>
                <li><strong>影响范围</strong>: {affected_area}</li>
            </ul>
            <p><strong>详细描述</strong>: {description}</p>
            <p><strong>建议措施</strong>: {recommended_action}</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """

    def _get_report_ready_template(self) -> str:
        """报告就绪邮件模板"""
        return """
        <html>
        <body>
            <h2>📊 报告已生成</h2>
            <p>您请求的报告已经生成完成：</p>
            <ul>
                <li><strong>报告名称</strong>: {report_name}</li>
                <li><strong>报告类型</strong>: {report_type}</li>
                <li><strong>生成时间</strong>: {generated_at}</li>
                <li><strong>数据范围</strong>: {data_range}</li>
            </ul>
            <p>请登录系统查看详细报告内容。</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """

    def _get_system_alert_template(self) -> str:
        """系统警报邮件模板"""
        return """
        <html>
        <body>
            <h2>⚠️ 系统警报</h2>
            <p>系统监控发现以下问题：</p>
            <ul>
                <li><strong>警报类型</strong>: {alert_type}</li>
                <li><strong>系统组件</strong>: {component}</li>
                <li><strong>当前状态</strong>: {status}</li>
                <li><strong>检测时间</strong>: {timestamp}</li>
            </ul>
            <p><strong>详细信息</strong>: {details}</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """

    def _get_backup_complete_template(self) -> str:
        """备份完成邮件模板"""
        return """
        <html>
        <body>
            <h2>✅ 备份完成</h2>
            <p>系统备份已成功完成：</p>
            <ul>
                <li><strong>备份类型</strong>: {backup_type}</li>
                <li><strong>备份大小</strong>: {backup_size}</li>
                <li><strong>备份时间</strong>: {backup_time}</li>
                <li><strong>存储位置</strong>: {storage_location}</li>
            </ul>
            <p>备份文件已安全存储，系统运行正常。</p>
            <hr>
            <p><small>此邮件由 Smart APS 系统自动发送</small></p>
        </body>
        </html>
        """

    def _get_production_alert_template(self) -> str:
        """生产警报邮件模板"""
        return """
        <html>
        <body>
            <h2>🏭 生产警报</h2>
            <p>生产线发现问题，请及时处理：</p>
            <ul>
                <li><strong>生产线</strong>: {production_line}</li>
                <li><strong>问题类型</strong>: {issue_type}</li>
                <li><strong>严重程度</strong>: {severity}</li>
                <li><strong>发生时间</strong>: {timestamp}</li>
            </ul>
            <p><strong>问题描述</strong>: {description}</p>
            <p><strong>建议措施</strong>: {recommended_action}</p>
            <hr>
            <p><small>此邮件由 Smart APS 生产监控系统自动发送</small></p>
        </body>
        </html>
        """

    def _get_equipment_alert_template(self) -> str:
        """设备警报邮件模板"""
        return """
        <html>
        <body>
            <h2>⚙️ 设备警报</h2>
            <p>设备状态异常，请及时检查：</p>
            <ul>
                <li><strong>设备编号</strong>: {equipment_id}</li>
                <li><strong>设备名称</strong>: {equipment_name}</li>
                <li><strong>当前状态</strong>: {status}</li>
                <li><strong>检测时间</strong>: {timestamp}</li>
                <li><strong>需要维护</strong>: {"是" if maintenance_required else "否"}</li>
            </ul>
            <p><strong>问题描述</strong>: {issue_description}</p>
            <hr>
            <p><small>此邮件由 Smart APS 设备监控系统自动发送</small></p>
        </body>
        </html>
        """

    def test_connection(self) -> Dict[str, Any]:
        """测试邮件连接"""
        if not self.enabled:
            return {
                "success": False,
                "message": "邮件功能未启用"
            }

        try:
            server = self._create_smtp_connection()
            server.quit()
            return {
                "success": True,
                "message": "邮件连接测试成功"
            }
        except Exception as e:
            return {
                "success": False,
                "message": f"邮件连接测试失败: {str(e)}"
            }

    def update_config(self, config: Dict[str, Any]):
        """更新邮件配置"""
        if "smtp_host" in config:
            self.smtp_host = config["smtp_host"]
        if "smtp_port" in config:
            self.smtp_port = config["smtp_port"]
        if "smtp_user" in config:
            self.smtp_user = config["smtp_user"]
        if "smtp_password" in config:
            self.smtp_password = config["smtp_password"]
        if "smtp_tls" in config:
            self.smtp_tls = config["smtp_tls"]
        if "smtp_ssl" in config:
            self.smtp_ssl = config["smtp_ssl"]
        if "email_from" in config:
            self.email_from = config["email_from"]
        if "enabled" in config:
            self.enabled = config["enabled"]


# 全局邮件服务实例
email_service = EmailService()
