# 📧 Smart APS 邮件功能使用示例

## 📋 概述

本文档提供Smart APS邮件功能的详细使用示例，展示如何在企业环境中使用邮件通知功能。

## 🔧 基础配置示例

### 1. 环境变量配置

```bash
# .env 文件配置
SMTP_HOST=mail.company.com
SMTP_PORT=25
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_password
SMTP_TLS=false
SMTP_SSL=false
EMAIL_FROM=Smart APS System <<EMAIL>>
EMAIL_ENABLED=true

# 收件人配置
EMAIL_ADMIN_RECIPIENTS=<EMAIL>,<EMAIL>
EMAIL_ALERT_RECIPIENTS=<EMAIL>,<EMAIL>
EMAIL_REPORT_RECIPIENTS=<EMAIL>,<EMAIL>
```

### 2. 前端配置示例

访问"邮件配置"页面，填写以下信息：
- **SMTP服务器**: mail.company.com
- **端口**: 25
- **用户名**: <EMAIL>
- **密码**: [企业邮箱密码]
- **管理员邮箱**: <EMAIL>,<EMAIL>
- **警报邮箱**: <EMAIL>,<EMAIL>
- **报告邮箱**: <EMAIL>,<EMAIL>

## 🏭 生产场景使用示例

### 1. 生产线异常通知

```python
from app.services.email_service import email_service

# 生产线L01出现设备故障
email_service.send_production_notification(
    production_line="L01",
    issue_type="设备故障",
    description="生产线L01的主轴电机出现异常，温度过高",
    severity="高"
)

# 自动发送到*****************,<EMAIL>
```

### 2. 设备状态异常通知

```python
# 设备Tank01液位异常
email_service.send_equipment_notification(
    equipment_id="Tank01",
    equipment_name="原料储罐01",
    status="异常",
    issue_description="液位传感器显示异常，可能存在泄漏"
)

# 自动发送到*****************,<EMAIL>
```

### 3. 质量异常警报

```python
# 产品质量检测异常
email_service.send_notification(
    notification_type="anomaly_alert",
    data={
        "anomaly_type": "质量异常",
        "severity": "中",
        "timestamp": "2024-01-15 14:30:00",
        "affected_area": "生产线L02",
        "description": "产品尺寸超出公差范围",
        "recommended_action": "停机检查模具，调整工艺参数"
    }
)
```

## 📊 报告通知示例

### 1. 日报生成通知

```python
# 日报生成完成通知
email_service.send_notification(
    notification_type="report_ready",
    data={
        "report_name": "生产日报",
        "report_type": "日报",
        "generated_at": "2024-01-15 18:00:00",
        "data_range": "2024-01-15 00:00:00 - 2024-01-15 23:59:59"
    }
)

# 自动发送到******************,<EMAIL>
```

### 2. 月度分析报告

```python
# 月度分析报告通知
email_service.send_notification(
    notification_type="report_ready",
    recipients=["<EMAIL>", "<EMAIL>"],
    data={
        "report_name": "月度生产分析报告",
        "report_type": "月报",
        "generated_at": "2024-01-31 23:00:00",
        "data_range": "2024-01-01 - 2024-01-31"
    }
)
```

## ⚙️ 系统管理示例

### 1. 系统备份通知

```python
# 系统备份完成通知
email_service.send_notification(
    notification_type="backup_complete",
    data={
        "backup_type": "完整备份",
        "backup_size": "2.5GB",
        "backup_time": "2024-01-15 02:00:00",
        "storage_location": "/backup/smartaps_20240115.bak"
    }
)

# 自动发送到*****************,<EMAIL>
```

### 2. 系统警报通知

```python
# 数据库连接异常
email_service.send_system_notification(
    title="数据库连接异常",
    message="主数据库连接超时，系统已切换到备用数据库",
    severity="高"
)

# 磁盘空间不足警报
email_service.send_system_notification(
    title="磁盘空间警报",
    message="服务器磁盘使用率达到85%，请及时清理",
    severity="中"
)
```

## 🔧 API调用示例

### 1. 发送自定义邮件

```python
import requests

# 发送自定义邮件
response = requests.post(
    "http://localhost:8000/api/v1/email/send",
    json={
        "to_emails": ["<EMAIL>"],
        "subject": "Smart APS 系统通知",
        "body": "这是一条系统通知消息",
        "html_body": "<h2>系统通知</h2><p>这是一条系统通知消息</p>"
    },
    headers={"Authorization": "Bearer your_token"}
)
```

### 2. 测试邮件连接

```python
# 测试邮件服务器连接
response = requests.get(
    "http://localhost:8000/api/v1/email/test",
    headers={"Authorization": "Bearer your_token"}
)

if response.json()["success"]:
    print("邮件服务器连接正常")
else:
    print(f"连接失败: {response.json()['message']}")
```

### 3. 获取邮件模板

```python
# 获取可用邮件模板
response = requests.get(
    "http://localhost:8000/api/v1/email/templates",
    headers={"Authorization": "Bearer your_token"}
)

templates = response.json()["templates"]
for template in templates:
    print(f"模板: {template['name']} - {template['description']}")
```

## 🎯 最佳实践示例

### 1. 批量通知处理

```python
# 批量处理生产异常
production_issues = [
    {"line": "L01", "issue": "设备故障", "severity": "高"},
    {"line": "L02", "issue": "质量异常", "severity": "中"},
    {"line": "L03", "issue": "原料不足", "severity": "低"}
]

for issue in production_issues:
    email_service.send_production_notification(
        production_line=issue["line"],
        issue_type=issue["issue"],
        description=f"生产线{issue['line']}发生{issue['issue']}",
        severity=issue["severity"]
    )
```

### 2. 条件通知发送

```python
# 根据严重程度决定收件人
def send_conditional_alert(severity, message):
    if severity == "高":
        recipients = ["<EMAIL>", "<EMAIL>"]
    elif severity == "中":
        recipients = ["<EMAIL>", "<EMAIL>"]
    else:
        recipients = ["<EMAIL>"]
    
    email_service.send_system_notification(
        title=f"系统警报 - {severity}",
        message=message,
        severity=severity,
        recipients=recipients
    )

# 使用示例
send_conditional_alert("高", "生产线全线停机")
send_conditional_alert("中", "设备温度异常")
send_conditional_alert("低", "原料库存不足")
```

### 3. 定时报告发送

```python
import schedule
import time

def send_daily_report():
    """发送日报"""
    email_service.send_notification(
        notification_type="report_ready",
        data={
            "report_name": "生产日报",
            "report_type": "日报",
            "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data_range": f"{datetime.now().strftime('%Y-%m-%d')} 全天"
        }
    )

def send_weekly_report():
    """发送周报"""
    email_service.send_notification(
        notification_type="report_ready",
        recipients=["<EMAIL>"],
        data={
            "report_name": "生产周报",
            "report_type": "周报",
            "generated_at": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "data_range": "本周"
        }
    )

# 定时任务设置
schedule.every().day.at("18:00").do(send_daily_report)
schedule.every().monday.at("09:00").do(send_weekly_report)

# 运行定时任务
while True:
    schedule.run_pending()
    time.sleep(60)
```

## 🔍 故障排除示例

### 1. 连接测试失败

```python
# 诊断邮件连接问题
def diagnose_email_connection():
    try:
        result = email_service.test_connection()
        if not result["success"]:
            print(f"连接失败: {result['message']}")
            
            # 检查常见问题
            print("请检查以下配置:")
            print(f"- SMTP服务器: {email_service.smtp_host}")
            print(f"- 端口: {email_service.smtp_port}")
            print(f"- 用户名: {email_service.smtp_user}")
            print(f"- TLS: {email_service.smtp_tls}")
            print(f"- SSL: {email_service.smtp_ssl}")
        else:
            print("邮件服务器连接正常")
    except Exception as e:
        print(f"连接测试异常: {str(e)}")

diagnose_email_connection()
```

### 2. 发送失败处理

```python
# 带重试机制的邮件发送
def send_email_with_retry(notification_type, data, max_retries=3):
    for attempt in range(max_retries):
        try:
            success = email_service.send_notification(notification_type, data=data)
            if success:
                print(f"邮件发送成功 (尝试 {attempt + 1})")
                return True
            else:
                print(f"邮件发送失败 (尝试 {attempt + 1})")
        except Exception as e:
            print(f"发送异常 (尝试 {attempt + 1}): {str(e)}")
        
        if attempt < max_retries - 1:
            time.sleep(5)  # 等待5秒后重试
    
    print("邮件发送最终失败")
    return False

# 使用示例
send_email_with_retry("system_alert", {
    "title": "系统警报",
    "message": "重要系统通知",
    "severity": "高"
})
```

## 📝 配置验证示例

```python
# 验证邮件配置
def validate_email_config():
    """验证邮件配置是否正确"""
    issues = []
    
    if not email_service.enabled:
        issues.append("邮件功能未启用")
    
    if not email_service.smtp_host:
        issues.append("SMTP服务器地址未配置")
    
    if not email_service.smtp_user:
        issues.append("SMTP用户名未配置")
    
    if not email_service.admin_recipients:
        issues.append("管理员邮箱未配置")
    
    if not email_service.alert_recipients:
        issues.append("警报邮箱未配置")
    
    if issues:
        print("配置问题:")
        for issue in issues:
            print(f"- {issue}")
        return False
    else:
        print("邮件配置验证通过")
        return True

# 系统启动时验证配置
if __name__ == "__main__":
    validate_email_config()
```

**📧 这些示例展示了Smart APS邮件功能在企业环境中的完整使用方法，涵盖了配置、发送、监控、故障排除等各个方面！**
