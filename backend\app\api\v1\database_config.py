"""
数据库配置管理API
支持前端界面配置数据库连接
"""

from fastapi import APIRouter, HTTPException, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, validator

from app.core.dynamic_database import (
    dynamic_db_manager, 
    DatabaseConfig, 
    DatabaseEngine
)
from app.api.dependencies import get_current_user

router = APIRouter()


class DatabaseConfigRequest(BaseModel):
    """数据库配置请求"""
    name: str
    engine_type: str
    host: str
    port: int
    username: str
    password: str
    database: str
    description: Optional[str] = ""
    connection_url: Optional[str] = None
    extra_params: Optional[Dict[str, Any]] = None
    
    @validator('engine_type')
    def validate_engine_type(cls, v):
        try:
            DatabaseEngine(v)
            return v
        except ValueError:
            raise ValueError(f"不支持的数据库引擎类型: {v}")
    
    @validator('port')
    def validate_port(cls, v):
        if not (1 <= v <= 65535):
            raise ValueError("端口号必须在1-65535之间")
        return v


class DatabaseConfigResponse(BaseModel):
    """数据库配置响应"""
    name: str
    engine_type: str
    host: str
    port: int
    username: str
    database: str
    description: str
    configured: bool
    healthy: Optional[bool] = None
    connection_info: Optional[Dict[str, Any]] = None


class DatabaseTestRequest(BaseModel):
    """数据库连接测试请求"""
    engine_type: str
    host: str
    port: int
    username: str
    password: str
    database: str
    connection_url: Optional[str] = None


@router.get("/configs", response_model=List[DatabaseConfigResponse])
async def get_database_configs(current_user = Depends(get_current_user)):
    """获取所有数据库配置"""
    try:
        configs = dynamic_db_manager.list_database_configs()
        health_status = await dynamic_db_manager.health_check_all()
        
        response_configs = []
        for config in configs:
            response_configs.append(DatabaseConfigResponse(
                name=config.name,
                engine_type=config.engine_type.value,
                host=config.host,
                port=config.port,
                username=config.username,
                database=config.database,
                description=config.description,
                configured=True,
                healthy=health_status.get(config.name, False)
            ))
        
        return response_configs
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库配置失败: {str(e)}")


@router.get("/configs/{db_name}", response_model=DatabaseConfigResponse)
async def get_database_config(db_name: str, current_user = Depends(get_current_user)):
    """获取指定数据库配置"""
    try:
        config = dynamic_db_manager.get_database_config(db_name)
        if not config:
            raise HTTPException(status_code=404, detail=f"数据库配置 {db_name} 不存在")
        
        health = await dynamic_db_manager.health_check(db_name)
        
        return DatabaseConfigResponse(
            name=config.name,
            engine_type=config.engine_type.value,
            host=config.host,
            port=config.port,
            username=config.username,
            database=config.database,
            description=config.description,
            configured=True,
            healthy=health
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库配置失败: {str(e)}")


@router.post("/configs")
async def create_database_config(
    config_request: DatabaseConfigRequest,
    current_user = Depends(get_current_user)
):
    """创建数据库配置"""
    try:
        # 检查数据库名称是否已存在
        if dynamic_db_manager.is_database_configured(config_request.name):
            raise HTTPException(status_code=400, detail=f"数据库配置 {config_request.name} 已存在")
        
        # 创建数据库配置
        config = DatabaseConfig(
            name=config_request.name,
            engine_type=DatabaseEngine(config_request.engine_type),
            host=config_request.host,
            port=config_request.port,
            username=config_request.username,
            password=config_request.password,
            database=config_request.database,
            description=config_request.description or "",
            connection_url=config_request.connection_url,
            extra_params=config_request.extra_params or {}
        )
        
        # 添加配置
        success = dynamic_db_manager.add_database_config(config)
        if not success:
            raise HTTPException(status_code=500, detail="添加数据库配置失败")
        
        return {
            "success": True,
            "message": f"数据库配置 {config_request.name} 创建成功",
            "data": {
                "name": config.name,
                "engine_type": config.engine_type.value
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建数据库配置失败: {str(e)}")


@router.put("/configs/{db_name}")
async def update_database_config(
    db_name: str,
    config_request: DatabaseConfigRequest,
    current_user = Depends(get_current_user)
):
    """更新数据库配置"""
    try:
        # 检查数据库配置是否存在
        if not dynamic_db_manager.is_database_configured(db_name):
            raise HTTPException(status_code=404, detail=f"数据库配置 {db_name} 不存在")
        
        # 移除旧配置
        dynamic_db_manager.remove_database_config(db_name)
        
        # 创建新配置
        config = DatabaseConfig(
            name=config_request.name,
            engine_type=DatabaseEngine(config_request.engine_type),
            host=config_request.host,
            port=config_request.port,
            username=config_request.username,
            password=config_request.password,
            database=config_request.database,
            description=config_request.description or "",
            connection_url=config_request.connection_url,
            extra_params=config_request.extra_params or {}
        )
        
        # 添加新配置
        success = dynamic_db_manager.add_database_config(config)
        if not success:
            raise HTTPException(status_code=500, detail="更新数据库配置失败")
        
        return {
            "success": True,
            "message": f"数据库配置 {db_name} 更新成功",
            "data": {
                "name": config.name,
                "engine_type": config.engine_type.value
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新数据库配置失败: {str(e)}")


@router.delete("/configs/{db_name}")
async def delete_database_config(db_name: str, current_user = Depends(get_current_user)):
    """删除数据库配置"""
    try:
        # 检查数据库配置是否存在
        if not dynamic_db_manager.is_database_configured(db_name):
            raise HTTPException(status_code=404, detail=f"数据库配置 {db_name} 不存在")
        
        # 不允许删除主数据库
        if db_name == "main":
            raise HTTPException(status_code=400, detail="不能删除主数据库配置")
        
        # 删除配置
        success = dynamic_db_manager.remove_database_config(db_name)
        if not success:
            raise HTTPException(status_code=500, detail="删除数据库配置失败")
        
        return {
            "success": True,
            "message": f"数据库配置 {db_name} 删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除数据库配置失败: {str(e)}")


@router.post("/test-connection")
async def test_database_connection(
    test_request: DatabaseTestRequest,
    current_user = Depends(get_current_user)
):
    """测试数据库连接"""
    try:
        # 创建临时配置进行测试
        temp_config = DatabaseConfig(
            name="temp_test",
            engine_type=DatabaseEngine(test_request.engine_type),
            host=test_request.host,
            port=test_request.port,
            username=test_request.username,
            password=test_request.password,
            database=test_request.database,
            connection_url=test_request.connection_url
        )
        
        # 尝试创建连接并测试
        from sqlalchemy.ext.asyncio import create_async_engine
        from sqlalchemy import text
        
        engine = create_async_engine(
            temp_config.get_connection_url(),
            echo=False,
            pool_size=1,
            max_overflow=0,
            pool_timeout=10,
            pool_recycle=3600,
            pool_pre_ping=True,
        )
        
        try:
            async with engine.begin() as conn:
                if test_request.engine_type == "oracle":
                    await conn.execute(text("SELECT 1 FROM DUAL"))
                else:
                    await conn.execute(text("SELECT 1"))
            
            await engine.dispose()
            
            return {
                "success": True,
                "message": "数据库连接测试成功",
                "connection_info": {
                    "engine_type": test_request.engine_type,
                    "host": test_request.host,
                    "port": test_request.port,
                    "database": test_request.database
                }
            }
        except Exception as conn_error:
            await engine.dispose()
            return {
                "success": False,
                "message": f"数据库连接测试失败: {str(conn_error)}",
                "error_type": "connection_error"
            }
            
    except Exception as e:
        return {
            "success": False,
            "message": f"连接测试失败: {str(e)}",
            "error_type": "config_error"
        }


@router.get("/engines")
async def get_supported_engines(current_user = Depends(get_current_user)):
    """获取支持的数据库引擎列表"""
    try:
        engines = dynamic_db_manager.get_supported_engines()
        engine_info = []
        
        for engine in engines:
            info = {
                "value": engine,
                "label": _get_engine_display_name(engine),
                "default_port": _get_engine_default_port(engine),
                "connection_example": _get_engine_connection_example(engine)
            }
            engine_info.append(info)
        
        return {
            "success": True,
            "data": engine_info
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库引擎列表失败: {str(e)}")


@router.post("/configs/{db_name}/health-check")
async def check_database_health(db_name: str, current_user = Depends(get_current_user)):
    """检查数据库健康状态"""
    try:
        if not dynamic_db_manager.is_database_configured(db_name):
            raise HTTPException(status_code=404, detail=f"数据库配置 {db_name} 不存在")
        
        health = await dynamic_db_manager.health_check(db_name)
        
        return {
            "success": True,
            "data": {
                "database_name": db_name,
                "healthy": health,
                "checked_at": "2024-01-01T12:00:00Z"  # 实际应该使用当前时间
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


def _get_engine_display_name(engine: str) -> str:
    """获取数据库引擎显示名称"""
    names = {
        "mysql": "MySQL",
        "oracle": "Oracle Database",
        "postgresql": "PostgreSQL",
        "sqlserver": "SQL Server",
        "sqlite": "SQLite"
    }
    return names.get(engine, engine.upper())


def _get_engine_default_port(engine: str) -> int:
    """获取数据库引擎默认端口"""
    ports = {
        "mysql": 3306,
        "oracle": 1521,
        "postgresql": 5432,
        "sqlserver": 1433,
        "sqlite": 0
    }
    return ports.get(engine, 3306)


def _get_engine_connection_example(engine: str) -> str:
    """获取数据库引擎连接示例"""
    examples = {
        "mysql": "mysql+aiomysql://user:password@localhost:3306/database?charset=utf8mb4",
        "oracle": "oracle+oracledb://user:password@localhost:1521/?service_name=ORCL",
        "postgresql": "postgresql+asyncpg://user:password@localhost:5432/database",
        "sqlserver": "mssql+aioodbc://user:password@localhost:1433/database?driver=ODBC+Driver+17+for+SQL+Server",
        "sqlite": "sqlite+aiosqlite:///path/to/database.db"
    }
    return examples.get(engine, "")
