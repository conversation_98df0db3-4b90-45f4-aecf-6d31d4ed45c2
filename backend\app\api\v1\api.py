"""
API v1 路由汇总
"""

from fastapi import APIRouter

from app.api.v1.endpoints import (
    auth,
    users,
    data_sources,
    file_upload,
    production_plans,
    equipment,
    llm_chat,
    system
)

# 导入数据库相关路由
from app.api.v1 import database, database_config

# 导入AI增强路由
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
from backend.routers import ai_enhancement

api_router = APIRouter()

# 认证相关
api_router.include_router(
    auth.router,
    prefix="/auth",
    tags=["认证"]
)

# 用户管理
api_router.include_router(
    users.router,
    prefix="/users",
    tags=["用户管理"]
)

# 数据源管理
api_router.include_router(
    data_sources.router,
    prefix="/data-sources",
    tags=["数据源管理"]
)

# 文件上传
api_router.include_router(
    file_upload.router,
    prefix="/upload",
    tags=["文件上传"]
)

# 生产计划
api_router.include_router(
    production_plans.router,
    prefix="/production-plans",
    tags=["生产计划"]
)

# 设备管理
api_router.include_router(
    equipment.router,
    prefix="/equipment",
    tags=["设备管理"]
)

# LLM聊天
api_router.include_router(
    llm_chat.router,
    prefix="/llm",
    tags=["智能助手"]
)

# 系统管理
api_router.include_router(
    system.router,
    prefix="/system",
    tags=["系统管理"]
)

# 数据库查询
api_router.include_router(
    database.router,
    prefix="/database",
    tags=["数据库查询"]
)

# 数据库配置管理
api_router.include_router(
    database_config.router,
    prefix="/database-config",
    tags=["数据库配置"]
)

# AI能力增强
api_router.include_router(
    ai_enhancement.router,
    prefix="/ai-enhancement",
    tags=["AI能力增强"]
)
