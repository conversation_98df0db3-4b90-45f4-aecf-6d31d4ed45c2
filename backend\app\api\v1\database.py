"""
多数据库查询API路由
"""

from fastapi import APIRouter, HTTPException, Query, Depends
from typing import List, Dict, Any, Optional
from pydantic import BaseModel

from app.services.database_query_service import (
    database_query_service,
    inventory_query_service,
    production_analysis_query_service
)
from app.core.database import DatabaseType
from app.api.dependencies import get_current_user

router = APIRouter()


class CustomQueryRequest(BaseModel):
    """自定义查询请求"""
    database_type: str
    query: str
    parameters: Optional[Dict[str, Any]] = None


class QueryResponse(BaseModel):
    """查询响应"""
    success: bool
    data: List[Dict[str, Any]]
    total_rows: int
    database_type: Optional[str] = None
    message: Optional[str] = None


@router.get("/status", response_model=Dict[str, Any])
async def get_database_status(current_user = Depends(get_current_user)):
    """获取所有数据库状态"""
    try:
        status = await database_query_service.get_database_status()
        return {
            "success": True,
            "data": status,
            "message": "数据库状态获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库状态失败: {str(e)}")


@router.get("/inventory/summary", response_model=QueryResponse)
async def get_inventory_summary(current_user = Depends(get_current_user)):
    """获取库存汇总信息"""
    try:
        data = await database_query_service.get_inventory_summary()
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="inventory",
            message="库存汇总数据获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取库存数据失败: {str(e)}")


@router.get("/inventory/low-stock", response_model=QueryResponse)
async def get_low_stock_items(
    threshold: int = Query(100, description="库存阈值"),
    current_user = Depends(get_current_user)
):
    """获取低库存商品"""
    try:
        data = await inventory_query_service.get_low_stock_items(threshold)
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="inventory",
            message=f"低库存商品数据获取成功（阈值: {threshold}）"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取低库存数据失败: {str(e)}")


@router.get("/inventory/expiring", response_model=QueryResponse)
async def get_expiring_items(
    days: int = Query(30, description="天数"),
    current_user = Depends(get_current_user)
):
    """获取即将过期的商品"""
    try:
        data = await inventory_query_service.get_expiring_items(days)
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="inventory",
            message=f"即将过期商品数据获取成功（{days}天内）"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取过期商品数据失败: {str(e)}")


@router.get("/production/efficiency", response_model=QueryResponse)
async def get_production_efficiency(
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD)"),
    current_user = Depends(get_current_user)
):
    """获取生产效率数据"""
    try:
        data = await database_query_service.get_production_efficiency_data(start_date, end_date)
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="production_analysis",
            message=f"生产效率数据获取成功（{start_date} 至 {end_date}）"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取生产效率数据失败: {str(e)}")


@router.get("/production/efficiency-trends", response_model=QueryResponse)
async def get_efficiency_trends(
    production_line: str = Query(..., description="生产线"),
    days: int = Query(30, description="天数"),
    current_user = Depends(get_current_user)
):
    """获取效率趋势"""
    try:
        data = await production_analysis_query_service.get_efficiency_trends(production_line, days)
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="production_analysis",
            message=f"效率趋势数据获取成功（{production_line}，{days}天）"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取效率趋势失败: {str(e)}")


@router.get("/production/quality", response_model=QueryResponse)
async def get_quality_metrics(
    start_date: str = Query(..., description="开始日期 (YYYY-MM-DD)"),
    end_date: str = Query(..., description="结束日期 (YYYY-MM-DD)"),
    current_user = Depends(get_current_user)
):
    """获取质量指标"""
    try:
        data = await production_analysis_query_service.get_quality_metrics(start_date, end_date)
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="production_analysis",
            message=f"质量指标数据获取成功（{start_date} 至 {end_date}）"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取质量指标失败: {str(e)}")


@router.get("/erp/orders", response_model=QueryResponse)
async def get_erp_orders(
    status: Optional[str] = Query(None, description="订单状态"),
    current_user = Depends(get_current_user)
):
    """获取ERP订单数据"""
    try:
        data = await database_query_service.get_erp_order_data(status)
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="erp",
            message=f"ERP订单数据获取成功" + (f"（状态: {status}）" if status else "")
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取ERP订单数据失败: {str(e)}")


@router.get("/mes/equipment", response_model=QueryResponse)
async def get_mes_equipment_status(current_user = Depends(get_current_user)):
    """获取MES设备状态"""
    try:
        data = await database_query_service.get_mes_equipment_status()
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type="mes",
            message="MES设备状态获取成功"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取MES设备状态失败: {str(e)}")


@router.post("/query/custom", response_model=QueryResponse)
async def execute_custom_query(
    request: CustomQueryRequest,
    current_user = Depends(get_current_user)
):
    """执行自定义查询"""
    try:
        # 验证数据库类型
        valid_types = [db_type.value for db_type in DatabaseType]
        if request.database_type not in valid_types:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的数据库类型: {request.database_type}。支持的类型: {valid_types}"
            )
        
        data = await database_query_service.execute_custom_query(
            request.database_type,
            request.query,
            request.parameters
        )
        
        return QueryResponse(
            success=True,
            data=data,
            total_rows=len(data),
            database_type=request.database_type,
            message="自定义查询执行成功"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"执行自定义查询失败: {str(e)}")


@router.get("/databases/configured", response_model=Dict[str, Any])
async def get_configured_databases(current_user = Depends(get_current_user)):
    """获取已配置的数据库列表"""
    try:
        from app.core.database import multi_db_manager
        
        configured_dbs = multi_db_manager.get_configured_databases()
        db_info = {}
        
        for db_type in configured_dbs:
            db_info[db_type.value] = {
                "name": db_type.value,
                "description": _get_database_description(db_type),
                "configured": True
            }
        
        return {
            "success": True,
            "data": {
                "configured_databases": db_info,
                "total_configured": len(configured_dbs),
                "available_types": [db_type.value for db_type in DatabaseType]
            },
            "message": "已配置数据库列表获取成功"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库列表失败: {str(e)}")


def _get_database_description(db_type: DatabaseType) -> str:
    """获取数据库描述"""
    descriptions = {
        DatabaseType.MAIN: "主数据库 - Smart APS核心数据",
        DatabaseType.INVENTORY: "库存数据库 - 库存管理数据",
        DatabaseType.PRODUCTION_ANALYSIS: "生产数据分析数据库 - 生产分析数据",
        DatabaseType.ERP: "ERP系统数据库 - 企业资源规划数据",
        DatabaseType.MES: "MES系统数据库 - 制造执行系统数据"
    }
    return descriptions.get(db_type, "未知数据库类型")
