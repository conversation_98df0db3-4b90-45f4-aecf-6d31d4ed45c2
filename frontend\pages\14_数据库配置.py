"""
数据库配置管理页面
支持前端界面配置多数据库连接
"""

import streamlit as st
import requests
import json
from typing import Dict, List, Any, Optional

# 页面配置
st.set_page_config(
    page_title="数据库配置 - Smart APS",
    page_icon="🗄️",
    layout="wide"
)

# 导入通用组件
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.auth import require_auth
from utils.api_client import api_request
from config.settings import API_BASE_URL

# 认证检查
require_auth()

# 页面标题
st.title("🗄️ 数据库配置管理")
st.markdown("---")

# 侧边栏
with st.sidebar:
    st.header("🔧 操作面板")
    
    # 操作选择
    operation = st.selectbox(
        "选择操作",
        ["查看配置", "添加配置", "编辑配置", "测试连接", "健康检查"],
        key="operation_select"
    )
    
    st.markdown("---")
    
    # 刷新按钮
    if st.button("🔄 刷新数据", key="refresh_data"):
        st.rerun()


def get_database_configs() -> List[Dict[str, Any]]:
    """获取数据库配置列表"""
    try:
        response = api_request("GET", f"{API_BASE_URL}/database-config/configs")
        if response and response.get("success", True):
            return response if isinstance(response, list) else response.get("data", [])
        return []
    except Exception as e:
        st.error(f"获取数据库配置失败: {str(e)}")
        return []


def get_supported_engines() -> List[Dict[str, Any]]:
    """获取支持的数据库引擎"""
    try:
        response = api_request("GET", f"{API_BASE_URL}/database-config/engines")
        if response and response.get("success"):
            return response.get("data", [])
        return []
    except Exception as e:
        st.error(f"获取数据库引擎列表失败: {str(e)}")
        return []


def test_database_connection(config: Dict[str, Any]) -> Dict[str, Any]:
    """测试数据库连接"""
    try:
        response = api_request("POST", f"{API_BASE_URL}/database-config/test-connection", data=config)
        return response or {"success": False, "message": "测试失败"}
    except Exception as e:
        return {"success": False, "message": f"测试连接失败: {str(e)}"}


def create_database_config(config: Dict[str, Any]) -> Dict[str, Any]:
    """创建数据库配置"""
    try:
        response = api_request("POST", f"{API_BASE_URL}/database-config/configs", data=config)
        return response or {"success": False, "message": "创建失败"}
    except Exception as e:
        return {"success": False, "message": f"创建配置失败: {str(e)}"}


def update_database_config(db_name: str, config: Dict[str, Any]) -> Dict[str, Any]:
    """更新数据库配置"""
    try:
        response = api_request("PUT", f"{API_BASE_URL}/database-config/configs/{db_name}", data=config)
        return response or {"success": False, "message": "更新失败"}
    except Exception as e:
        return {"success": False, "message": f"更新配置失败: {str(e)}"}


def delete_database_config(db_name: str) -> Dict[str, Any]:
    """删除数据库配置"""
    try:
        response = api_request("DELETE", f"{API_BASE_URL}/database-config/configs/{db_name}")
        return response or {"success": False, "message": "删除失败"}
    except Exception as e:
        return {"success": False, "message": f"删除配置失败: {str(e)}"}


def check_database_health(db_name: str) -> Dict[str, Any]:
    """检查数据库健康状态"""
    try:
        response = api_request("POST", f"{API_BASE_URL}/database-config/configs/{db_name}/health-check")
        return response or {"success": False, "message": "健康检查失败"}
    except Exception as e:
        return {"success": False, "message": f"健康检查失败: {str(e)}"}


def render_database_config_form(config: Optional[Dict[str, Any]] = None, is_edit: bool = False):
    """渲染数据库配置表单"""
    
    # 获取支持的数据库引擎
    engines = get_supported_engines()
    engine_options = {engine["label"]: engine["value"] for engine in engines}
    engine_ports = {engine["value"]: engine["default_port"] for engine in engines}
    
    with st.form("database_config_form"):
        st.subheader("📝 数据库配置信息")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 基本信息
            name = st.text_input(
                "数据库名称",
                value=config.get("name", "") if config else "",
                help="用于标识数据库的唯一名称",
                disabled=is_edit
            )
            
            engine_label = st.selectbox(
                "数据库类型",
                options=list(engine_options.keys()),
                index=list(engine_options.values()).index(config.get("engine_type", "mysql")) if config else 0,
                help="选择数据库引擎类型"
            )
            engine_type = engine_options[engine_label]
            
            host = st.text_input(
                "主机地址",
                value=config.get("host", "localhost") if config else "localhost",
                help="数据库服务器地址"
            )
            
            port = st.number_input(
                "端口号",
                min_value=1,
                max_value=65535,
                value=config.get("port", engine_ports.get(engine_type, 3306)) if config else engine_ports.get(engine_type, 3306),
                help="数据库服务器端口"
            )
        
        with col2:
            # 连接信息
            username = st.text_input(
                "用户名",
                value=config.get("username", "") if config else "",
                help="数据库用户名"
            )
            
            password = st.text_input(
                "密码",
                type="password",
                help="数据库密码"
            )
            
            database = st.text_input(
                "数据库名",
                value=config.get("database", "") if config else "",
                help="要连接的数据库名称"
            )
            
            description = st.text_area(
                "描述",
                value=config.get("description", "") if config else "",
                help="数据库配置的描述信息"
            )
        
        # 高级选项
        with st.expander("🔧 高级选项"):
            use_custom_url = st.checkbox("使用自定义连接字符串")
            
            if use_custom_url:
                connection_url = st.text_input(
                    "连接字符串",
                    help="完整的数据库连接字符串，如果提供则优先使用"
                )
            else:
                connection_url = None
            
            # 额外参数
            extra_params_json = st.text_area(
                "额外参数 (JSON格式)",
                value="{}",
                help="额外的连接参数，JSON格式"
            )
        
        # 表单按钮
        col1, col2, col3 = st.columns([1, 1, 1])
        
        with col1:
            test_button = st.form_submit_button("🔍 测试连接", type="secondary")
        
        with col2:
            if is_edit:
                submit_button = st.form_submit_button("💾 更新配置", type="primary")
            else:
                submit_button = st.form_submit_button("➕ 创建配置", type="primary")
        
        with col3:
            if is_edit:
                delete_button = st.form_submit_button("🗑️ 删除配置", type="secondary")
            else:
                delete_button = False
        
        # 处理表单提交
        if test_button or submit_button:
            # 验证必填字段
            if not all([name, host, username, password, database]):
                st.error("请填写所有必填字段")
                return
            
            # 解析额外参数
            try:
                extra_params = json.loads(extra_params_json) if extra_params_json.strip() else {}
            except json.JSONDecodeError:
                st.error("额外参数格式错误，请输入有效的JSON")
                return
            
            # 构建配置
            config_data = {
                "name": name,
                "engine_type": engine_type,
                "host": host,
                "port": int(port),
                "username": username,
                "password": password,
                "database": database,
                "description": description,
                "connection_url": connection_url,
                "extra_params": extra_params
            }
            
            if test_button:
                # 测试连接
                with st.spinner("正在测试连接..."):
                    result = test_database_connection(config_data)
                
                if result.get("success"):
                    st.success("✅ 数据库连接测试成功！")
                    st.json(result.get("connection_info", {}))
                else:
                    st.error(f"❌ 连接测试失败: {result.get('message', '未知错误')}")
            
            elif submit_button:
                # 创建或更新配置
                with st.spinner("正在保存配置..."):
                    if is_edit:
                        result = update_database_config(name, config_data)
                    else:
                        result = create_database_config(config_data)
                
                if result.get("success"):
                    st.success(f"✅ 数据库配置{'更新' if is_edit else '创建'}成功！")
                    st.rerun()
                else:
                    st.error(f"❌ 配置{'更新' if is_edit else '创建'}失败: {result.get('message', '未知错误')}")
        
        elif delete_button and is_edit:
            # 删除配置
            if st.session_state.get("confirm_delete"):
                with st.spinner("正在删除配置..."):
                    result = delete_database_config(name)
                
                if result.get("success"):
                    st.success("✅ 数据库配置删除成功！")
                    st.session_state.pop("confirm_delete", None)
                    st.rerun()
                else:
                    st.error(f"❌ 删除配置失败: {result.get('message', '未知错误')}")
            else:
                st.warning("⚠️ 确认删除此数据库配置？")
                if st.button("确认删除", key="confirm_delete_btn"):
                    st.session_state["confirm_delete"] = True
                    st.rerun()


def render_database_list():
    """渲染数据库配置列表"""
    st.subheader("📊 数据库配置列表")
    
    configs = get_database_configs()
    
    if not configs:
        st.info("暂无数据库配置")
        return
    
    # 创建表格数据
    table_data = []
    for config in configs:
        status_icon = "🟢" if config.get("healthy") else "🔴" if config.get("healthy") is False else "⚪"
        table_data.append({
            "名称": config.get("name", ""),
            "类型": config.get("engine_type", "").upper(),
            "主机": config.get("host", ""),
            "端口": config.get("port", ""),
            "数据库": config.get("database", ""),
            "状态": status_icon,
            "描述": config.get("description", "")[:50] + "..." if len(config.get("description", "")) > 50 else config.get("description", "")
        })
    
    # 显示表格
    st.dataframe(
        table_data,
        use_container_width=True,
        hide_index=True
    )
    
    # 操作按钮
    st.markdown("### 🔧 配置操作")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        selected_config = st.selectbox(
            "选择配置",
            options=[config["name"] for config in configs],
            key="selected_config_for_operation"
        )
    
    with col2:
        if st.button("✏️ 编辑配置", key="edit_config_btn"):
            st.session_state["edit_config"] = selected_config
            st.rerun()
    
    with col3:
        if st.button("🔍 健康检查", key="health_check_btn"):
            with st.spinner("正在检查健康状态..."):
                result = check_database_health(selected_config)
            
            if result.get("success"):
                health_data = result.get("data", {})
                if health_data.get("healthy"):
                    st.success(f"✅ {selected_config} 数据库连接正常")
                else:
                    st.error(f"❌ {selected_config} 数据库连接异常")
            else:
                st.error(f"健康检查失败: {result.get('message', '未知错误')}")


# 主页面逻辑
if operation == "查看配置":
    render_database_list()

elif operation == "添加配置":
    st.subheader("➕ 添加数据库配置")
    render_database_config_form()

elif operation == "编辑配置":
    if "edit_config" in st.session_state:
        config_name = st.session_state["edit_config"]
        configs = get_database_configs()
        config = next((c for c in configs if c["name"] == config_name), None)
        
        if config:
            st.subheader(f"✏️ 编辑数据库配置: {config_name}")
            render_database_config_form(config, is_edit=True)
        else:
            st.error("配置不存在")
    else:
        st.info("请先从配置列表中选择要编辑的配置")
        render_database_list()

elif operation == "测试连接":
    st.subheader("🔍 测试数据库连接")
    st.info("填写数据库连接信息进行连接测试")
    render_database_config_form()

elif operation == "健康检查":
    st.subheader("🏥 数据库健康检查")
    
    configs = get_database_configs()
    if configs:
        # 批量健康检查
        if st.button("🔍 检查所有数据库", key="check_all_health"):
            results = {}
            progress_bar = st.progress(0)
            
            for i, config in enumerate(configs):
                with st.spinner(f"正在检查 {config['name']}..."):
                    result = check_database_health(config["name"])
                    results[config["name"]] = result
                
                progress_bar.progress((i + 1) / len(configs))
            
            # 显示结果
            st.markdown("### 📊 健康检查结果")
            
            for config_name, result in results.items():
                if result.get("success"):
                    health_data = result.get("data", {})
                    if health_data.get("healthy"):
                        st.success(f"✅ {config_name}: 连接正常")
                    else:
                        st.error(f"❌ {config_name}: 连接异常")
                else:
                    st.error(f"❌ {config_name}: {result.get('message', '检查失败')}")
        
        # 单个健康检查
        st.markdown("---")
        st.markdown("### 🔍 单个数据库检查")
        render_database_list()
    else:
        st.info("暂无数据库配置可检查")

# 页面底部信息
st.markdown("---")
st.markdown("""
### 💡 使用说明

1. **查看配置**: 查看所有已配置的数据库连接
2. **添加配置**: 添加新的数据库连接配置
3. **编辑配置**: 修改现有的数据库配置
4. **测试连接**: 测试数据库连接是否正常
5. **健康检查**: 检查数据库连接的健康状态

**支持的数据库类型**: MySQL, Oracle, PostgreSQL, SQL Server, SQLite
""")
