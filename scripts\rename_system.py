#!/usr/bin/env python3
"""
系统名称替换脚本
将 Smart APS 替换为 Smart Planning
"""

import os
import re
import glob
from pathlib import Path
from typing import List, Dict, Tuple

class SystemRenamer:
    def __init__(self):
        self.root_dir = Path(__file__).parent.parent
        self.replacements = {
            # 系统名称替换
            "Smart APS": "Smart Planning",
            "smart-aps": "smart-planning",
            "smartaps": "smartplanning",
            "Smart APS系统": "Smart Planning系统",
            "Smart APS智能生产管理系统": "Smart Planning智能生产管理系统",
            "Smart APS 系统": "Smart Planning 系统",
            "Smart APS 智能生产管理系统": "Smart Planning 智能生产管理系统",
            "Smart APS开发团队": "Smart Planning开发团队",
            "Smart APS 开发团队": "Smart Planning 开发团队",
            
            # 邮件相关替换
            "Smart APS System": "Smart Planning System",
            "<EMAIL>": "<EMAIL>",
            "Smart APS <": "Smart Planning <",
            "Smart APS System <": "Smart Planning System <",
            
            # 文档和注释替换
            "APS": "Planning",  # 需要谨慎处理，避免误替换
            
            # URL和链接替换
            "smart-aps/": "smart-planning/",
            "smartaps.com": "smartplanning.com",
            
            # 项目名称替换
            "PROJECT_NAME=smart-aps": "PROJECT_NAME=smart-planning",
        }
        
        # 需要排除的文件和目录
        self.exclude_patterns = [
            "*.pyc",
            "__pycache__",
            ".git",
            "node_modules",
            "*.log",
            "*.tmp",
            ".env",
            "scripts/rename_system.py"  # 排除自身
        ]
        
        # 需要处理的文件扩展名
        self.include_extensions = [
            ".py", ".md", ".txt", ".yml", ".yaml", ".json", 
            ".sh", ".bat", ".env.example", ".dockerfile", 
            ".html", ".js", ".css", ".sql"
        ]
    
    def should_process_file(self, file_path: Path) -> bool:
        """判断是否应该处理该文件"""
        # 检查是否在排除列表中
        for pattern in self.exclude_patterns:
            if file_path.match(pattern):
                return False
        
        # 检查文件扩展名
        if file_path.suffix in self.include_extensions:
            return True
        
        # 检查特殊文件名
        special_files = ["Dockerfile", "README", "LICENSE"]
        if file_path.name in special_files:
            return True
        
        return False
    
    def get_all_files(self) -> List[Path]:
        """获取所有需要处理的文件"""
        files = []
        for root, dirs, filenames in os.walk(self.root_dir):
            # 排除特定目录
            dirs[:] = [d for d in dirs if d not in ['.git', '__pycache__', 'node_modules']]
            
            for filename in filenames:
                file_path = Path(root) / filename
                if self.should_process_file(file_path):
                    files.append(file_path)
        
        return files
    
    def replace_in_file(self, file_path: Path) -> Tuple[bool, int]:
        """在文件中进行替换"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            replacement_count = 0
            
            # 执行替换
            for old_text, new_text in self.replacements.items():
                if old_text in content:
                    # 特殊处理 "APS" 替换，避免误替换
                    if old_text == "APS":
                        # 只替换独立的 APS 词汇，避免替换 CAPS、MAPS 等
                        pattern = r'\bAPS\b'
                        matches = re.findall(pattern, content)
                        if matches:
                            content = re.sub(pattern, new_text, content)
                            replacement_count += len(matches)
                    else:
                        count = content.count(old_text)
                        content = content.replace(old_text, new_text)
                        replacement_count += count
            
            # 如果有替换，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return True, replacement_count
            
            return False, 0
            
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            return False, 0
    
    def run(self):
        """执行替换"""
        print("🔄 开始系统名称替换...")
        print(f"📁 工作目录: {self.root_dir}")
        
        files = self.get_all_files()
        print(f"📋 找到 {len(files)} 个文件需要处理")
        
        processed_files = 0
        total_replacements = 0
        
        for file_path in files:
            try:
                changed, count = self.replace_in_file(file_path)
                if changed:
                    processed_files += 1
                    total_replacements += count
                    print(f"✅ {file_path.relative_to(self.root_dir)} - {count} 处替换")
            except Exception as e:
                print(f"❌ 处理文件失败: {file_path} - {e}")
        
        print(f"\n🎉 替换完成!")
        print(f"📊 统计信息:")
        print(f"   - 处理文件数: {processed_files}")
        print(f"   - 总替换次数: {total_replacements}")
        print(f"   - 扫描文件数: {len(files)}")
        
        # 显示替换规则
        print(f"\n📝 替换规则:")
        for old, new in self.replacements.items():
            print(f"   - '{old}' → '{new}'")

if __name__ == "__main__":
    renamer = SystemRenamer()
    renamer.run()
