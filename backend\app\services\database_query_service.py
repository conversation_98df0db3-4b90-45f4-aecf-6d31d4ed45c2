"""
多数据库查询服务
支持配置化的多数据库查询
"""

import logging
from typing import Dict, List, Any, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import multi_db_manager, DatabaseType
from app.core.dynamic_database import dynamic_db_manager
from app.core.config import settings

logger = logging.getLogger(__name__)


class DatabaseQueryService:
    """数据库查询服务"""

    def __init__(self):
        self.db_manager = multi_db_manager

    async def query_inventory_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询库存数据库"""
        return await self._execute_query(DatabaseType.INVENTORY, query, params)

    async def query_production_analysis_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询生产数据分析数据库"""
        return await self._execute_query(DatabaseType.PRODUCTION_ANALYSIS, query, params)

    async def query_erp_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询ERP系统数据库"""
        return await self._execute_query(DatabaseType.ERP, query, params)

    async def query_mes_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询MES系统数据库"""
        return await self._execute_query(DatabaseType.MES, query, params)

    async def query_main_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询主数据库"""
        return await self._execute_query(DatabaseType.MAIN, query, params)

    async def _execute_query(self, db_type: DatabaseType, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """执行数据库查询"""
        try:
            # 检查数据库是否配置
            if not self.db_manager.is_database_configured(db_type):
                raise ValueError(f"数据库 {db_type.value} 未配置")

            async with self.db_manager.get_session(db_type) as session:
                result = await session.execute(text(query), params or {})

                # 转换结果为字典列表
                columns = result.keys()
                rows = result.fetchall()

                return [dict(zip(columns, row)) for row in rows]

        except Exception as e:
            logger.error(f"查询数据库 {db_type.value} 失败: {str(e)}")
            raise

    async def get_inventory_summary(self) -> List[Dict[str, Any]]:
        """获取库存汇总信息"""
        query = """
        SELECT
            item_code,
            item_name,
            SUM(quantity) as total_quantity,
            unit,
            location,
            status
        FROM inventory
        WHERE status = 'available'
        GROUP BY item_code, item_name, unit, location, status
        ORDER BY total_quantity DESC
        """
        return await self.query_inventory_data(query)

    async def get_production_efficiency_data(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取生产效率数据"""
        query = """
        SELECT
            production_line,
            DATE(production_date) as date,
            AVG(efficiency) as avg_efficiency,
            SUM(output_quantity) as total_output,
            COUNT(*) as batch_count
        FROM production_records
        WHERE production_date BETWEEN :start_date AND :end_date
        GROUP BY production_line, DATE(production_date)
        ORDER BY date DESC, production_line
        """
        params = {"start_date": start_date, "end_date": end_date}
        return await self.query_production_analysis_data(query, params)

    async def get_erp_order_data(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取ERP订单数据"""
        base_query = """
        SELECT
            order_id,
            customer_name,
            product_code,
            quantity,
            order_date,
            delivery_date,
            status,
            priority
        FROM sales_orders
        """

        if status:
            query = base_query + " WHERE status = :status ORDER BY order_date DESC"
            params = {"status": status}
        else:
            query = base_query + " ORDER BY order_date DESC"
            params = None

        return await self.query_erp_data(query, params)

    async def get_mes_equipment_status(self) -> List[Dict[str, Any]]:
        """获取MES设备状态"""
        query = """
        SELECT
            equipment_code,
            equipment_name,
            status,
            current_efficiency,
            last_maintenance_date,
            next_maintenance_date,
            location
        FROM equipment_status
        ORDER BY equipment_code
        """
        return await self.query_mes_data(query)

    async def get_database_status(self) -> Dict[str, Any]:
        """获取所有数据库状态"""
        status = {}

        for db_type in DatabaseType:
            if self.db_manager.is_database_configured(db_type):
                try:
                    health = await self.db_manager.health_check(db_type)
                    conn_info = await self.db_manager.get_connection_info(db_type)
                    status[db_type.value] = {
                        "configured": True,
                        "healthy": health,
                        "connection_info": conn_info
                    }
                except Exception as e:
                    status[db_type.value] = {
                        "configured": True,
                        "healthy": False,
                        "error": str(e)
                    }
            else:
                status[db_type.value] = {
                    "configured": False,
                    "healthy": None
                }

        return status

    async def execute_custom_query(self, db_type: str, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """执行自定义查询"""
        try:
            # 首先尝试使用枚举类型
            db_enum = DatabaseType(db_type)
            return await self._execute_query(db_enum, query, params)
        except ValueError:
            # 如果枚举中没有，尝试使用动态数据库管理器
            if dynamic_db_manager.is_database_configured(db_type):
                return await dynamic_db_manager.execute_query(db_type, query, params)
            else:
                raise ValueError(f"不支持的数据库类型: {db_type}")

    async def query_pci_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询PCI数据库"""
        if dynamic_db_manager.is_database_configured("pci"):
            return await dynamic_db_manager.execute_query("pci", query, params)
        else:
            # 回退到主数据库的PCI表
            return await self.query_main_data(query, params)

    async def query_quality_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询质量数据库"""
        if dynamic_db_manager.is_database_configured("quality"):
            return await dynamic_db_manager.execute_query("quality", query, params)
        else:
            return await self.query_main_data(query, params)

    async def query_logistics_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询物流数据库"""
        if dynamic_db_manager.is_database_configured("logistics"):
            return await dynamic_db_manager.execute_query("logistics", query, params)
        else:
            return await self.query_main_data(query, params)

    async def query_oracle_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询Oracle数据库"""
        # 查找配置的Oracle数据库
        oracle_dbs = [name for name, config in dynamic_db_manager.database_configs.items()
                     if config.engine_type.value == "oracle"]

        if oracle_dbs:
            # 使用第一个找到的Oracle数据库
            return await dynamic_db_manager.execute_query(oracle_dbs[0], query, params)
        else:
            raise ValueError("未配置Oracle数据库")

    async def query_any_database(self, db_name: str, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询任意已配置的数据库"""
        if dynamic_db_manager.is_database_configured(db_name):
            return await dynamic_db_manager.execute_query(db_name, query, params)
        else:
            raise ValueError(f"数据库 {db_name} 未配置")

    async def get_all_database_status(self) -> Dict[str, Any]:
        """获取所有数据库状态（包括动态配置的）"""
        # 获取静态配置的数据库状态
        static_status = await self.get_database_status()

        # 获取动态配置的数据库状态
        dynamic_status = await dynamic_db_manager.health_check_all()

        # 合并状态
        all_status = static_status.copy()
        for db_name, health in dynamic_status.items():
            if db_name not in all_status:
                config = dynamic_db_manager.get_database_config(db_name)
                all_status[db_name] = {
                    "configured": True,
                    "healthy": health,
                    "engine_type": config.engine_type.value if config else "unknown",
                    "description": config.description if config else "",
                    "source": "dynamic"
                }

        return all_status

    async def list_available_databases(self) -> List[Dict[str, Any]]:
        """列出所有可用的数据库"""
        databases = []

        # 静态配置的数据库
        for db_type in DatabaseType:
            if self.db_manager.is_database_configured(db_type):
                databases.append({
                    "name": db_type.value,
                    "type": "static",
                    "engine": "mysql",  # 默认
                    "configured": True
                })

        # 动态配置的数据库
        for config in dynamic_db_manager.list_database_configs():
            databases.append({
                "name": config.name,
                "type": "dynamic",
                "engine": config.engine_type.value,
                "description": config.description,
                "configured": True
            })

        return databases


class InventoryQueryService:
    """库存查询服务"""

    def __init__(self):
        self.db_service = DatabaseQueryService()

    async def get_low_stock_items(self, threshold: int = 100) -> List[Dict[str, Any]]:
        """获取低库存商品"""
        query = """
        SELECT
            item_code,
            item_name,
            SUM(quantity) as total_quantity,
            unit,
            :threshold as threshold
        FROM inventory
        WHERE status = 'available'
        GROUP BY item_code, item_name, unit
        HAVING SUM(quantity) < :threshold
        ORDER BY total_quantity ASC
        """
        params = {"threshold": threshold}
        return await self.db_service.query_inventory_data(query, params)

    async def get_expiring_items(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取即将过期的商品"""
        query = """
        SELECT
            item_code,
            item_name,
            quantity,
            expiry_date,
            DATEDIFF(expiry_date, CURDATE()) as days_to_expire
        FROM inventory
        WHERE status = 'available'
        AND expiry_date IS NOT NULL
        AND DATEDIFF(expiry_date, CURDATE()) <= :days
        ORDER BY days_to_expire ASC
        """
        params = {"days": days}
        return await self.db_service.query_inventory_data(query, params)


class ProductionAnalysisQueryService:
    """生产数据分析查询服务"""

    def __init__(self):
        self.db_service = DatabaseQueryService()

    async def get_efficiency_trends(self, production_line: str, days: int = 30) -> List[Dict[str, Any]]:
        """获取效率趋势"""
        query = """
        SELECT
            DATE(production_date) as date,
            AVG(efficiency) as avg_efficiency,
            MIN(efficiency) as min_efficiency,
            MAX(efficiency) as max_efficiency,
            COUNT(*) as batch_count
        FROM production_records
        WHERE production_line = :production_line
        AND production_date >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
        GROUP BY DATE(production_date)
        ORDER BY date ASC
        """
        params = {"production_line": production_line, "days": days}
        return await self.db_service.query_production_analysis_data(query, params)

    async def get_quality_metrics(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取质量指标"""
        query = """
        SELECT
            production_line,
            product_code,
            AVG(quality_score) as avg_quality,
            SUM(defect_count) as total_defects,
            SUM(output_quantity) as total_output,
            (SUM(defect_count) / SUM(output_quantity) * 100) as defect_rate
        FROM production_records
        WHERE production_date BETWEEN :start_date AND :end_date
        GROUP BY production_line, product_code
        ORDER BY defect_rate ASC
        """
        params = {"start_date": start_date, "end_date": end_date}
        return await self.db_service.query_production_analysis_data(query, params)


class PCIQueryService:
    """PCI数据查询服务"""

    def __init__(self):
        self.db_service = DatabaseQueryService()

    async def get_pci_summary(self) -> List[Dict[str, Any]]:
        """获取PCI数据汇总"""
        query = """
        SELECT
            item_code,
            item_name,
            SUM(quantity) as total_quantity,
            unit,
            AVG(age_days) as avg_age_days,
            MAX(age_days) as max_age_days,
            status,
            location
        FROM pci_data
        WHERE status = 'available'
        GROUP BY item_code, item_name, unit, status, location
        ORDER BY max_age_days DESC
        """
        return await self.db_service.query_pci_data(query)

    async def get_fifo_priority_items(self) -> List[Dict[str, Any]]:
        """获取FIFO优先级商品"""
        query = """
        SELECT
            item_code,
            item_name,
            quantity,
            age_days,
            yield_rate,
            shipping_urgency,
            gu_factor,
            fifo_priority,
            location
        FROM pci_data
        WHERE status = 'available' AND age_days > 180
        ORDER BY fifo_priority ASC, age_days DESC
        """
        return await self.db_service.query_pci_data(query)

    async def get_consumption_strategy(self, item_code: str) -> List[Dict[str, Any]]:
        """获取消耗策略"""
        query = """
        SELECT
            item_code,
            item_name,
            quantity,
            age_days,
            yield_rate,
            shipping_urgency,
            gu_factor,
            CASE
                WHEN age_days > 180 THEN 'urgent'
                WHEN age_days > 120 THEN 'high'
                WHEN age_days > 60 THEN 'medium'
                ELSE 'normal'
            END as consumption_priority,
            location,
            batch_number
        FROM pci_data
        WHERE item_code = :item_code AND status = 'available'
        ORDER BY age_days DESC
        """
        params = {"item_code": item_code}
        return await self.db_service.query_pci_data(query, params)


class MultiDatabaseQueryService:
    """多数据库统一查询服务"""

    def __init__(self):
        self.db_service = DatabaseQueryService()

    async def query_with_fallback(self, primary_db: str, fallback_db: str, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """带回退的查询"""
        try:
            # 尝试主数据库
            return await self.db_service.query_any_database(primary_db, query, params)
        except Exception as e:
            logger.warning(f"主数据库 {primary_db} 查询失败，尝试回退数据库 {fallback_db}: {str(e)}")
            return await self.db_service.query_any_database(fallback_db, query, params)

    async def cross_database_query(self, queries: Dict[str, Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """跨数据库查询"""
        results = {}

        for db_name, query_info in queries.items():
            try:
                query = query_info.get("query", "")
                params = query_info.get("params", {})

                if dynamic_db_manager.is_database_configured(db_name):
                    results[db_name] = await dynamic_db_manager.execute_query(db_name, query, params)
                else:
                    results[db_name] = {"error": f"数据库 {db_name} 未配置"}
            except Exception as e:
                results[db_name] = {"error": str(e)}

        return results

    async def aggregate_cross_database_data(self, db_queries: Dict[str, str], aggregation_func: str = "sum") -> Dict[str, Any]:
        """跨数据库数据聚合"""
        all_data = []

        for db_name, query in db_queries.items():
            try:
                data = await self.db_service.query_any_database(db_name, query)
                all_data.extend(data)
            except Exception as e:
                logger.error(f"查询数据库 {db_name} 失败: {str(e)}")

        # 简单的聚合示例
        if aggregation_func == "sum" and all_data:
            # 假设数据有数值字段需要求和
            numeric_fields = [k for k, v in all_data[0].items() if isinstance(v, (int, float))]
            aggregated = {}
            for field in numeric_fields:
                aggregated[field] = sum(row.get(field, 0) for row in all_data)
            return aggregated

        return {"total_records": len(all_data), "data": all_data}


# 全局服务实例
database_query_service = DatabaseQueryService()
inventory_query_service = InventoryQueryService()
production_analysis_query_service = ProductionAnalysisQueryService()
pci_query_service = PCIQueryService()
multi_database_query_service = MultiDatabaseQueryService()
