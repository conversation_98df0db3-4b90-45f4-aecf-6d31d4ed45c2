"""
多数据库查询服务
支持配置化的多数据库查询
"""

import logging
from typing import Dict, List, Any, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import multi_db_manager, DatabaseType
from app.core.config import settings

logger = logging.getLogger(__name__)


class DatabaseQueryService:
    """数据库查询服务"""
    
    def __init__(self):
        self.db_manager = multi_db_manager
    
    async def query_inventory_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询库存数据库"""
        return await self._execute_query(DatabaseType.INVENTORY, query, params)
    
    async def query_production_analysis_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询生产数据分析数据库"""
        return await self._execute_query(DatabaseType.PRODUCTION_ANALYSIS, query, params)
    
    async def query_erp_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询ERP系统数据库"""
        return await self._execute_query(DatabaseType.ERP, query, params)
    
    async def query_mes_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询MES系统数据库"""
        return await self._execute_query(DatabaseType.MES, query, params)
    
    async def query_main_data(self, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """查询主数据库"""
        return await self._execute_query(DatabaseType.MAIN, query, params)
    
    async def _execute_query(self, db_type: DatabaseType, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """执行数据库查询"""
        try:
            # 检查数据库是否配置
            if not self.db_manager.is_database_configured(db_type):
                raise ValueError(f"数据库 {db_type.value} 未配置")
            
            async with self.db_manager.get_session(db_type) as session:
                result = await session.execute(text(query), params or {})
                
                # 转换结果为字典列表
                columns = result.keys()
                rows = result.fetchall()
                
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"查询数据库 {db_type.value} 失败: {str(e)}")
            raise
    
    async def get_inventory_summary(self) -> List[Dict[str, Any]]:
        """获取库存汇总信息"""
        query = """
        SELECT 
            item_code,
            item_name,
            SUM(quantity) as total_quantity,
            unit,
            location,
            status
        FROM inventory 
        WHERE status = 'available'
        GROUP BY item_code, item_name, unit, location, status
        ORDER BY total_quantity DESC
        """
        return await self.query_inventory_data(query)
    
    async def get_production_efficiency_data(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取生产效率数据"""
        query = """
        SELECT 
            production_line,
            DATE(production_date) as date,
            AVG(efficiency) as avg_efficiency,
            SUM(output_quantity) as total_output,
            COUNT(*) as batch_count
        FROM production_records 
        WHERE production_date BETWEEN :start_date AND :end_date
        GROUP BY production_line, DATE(production_date)
        ORDER BY date DESC, production_line
        """
        params = {"start_date": start_date, "end_date": end_date}
        return await self.query_production_analysis_data(query, params)
    
    async def get_erp_order_data(self, status: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取ERP订单数据"""
        base_query = """
        SELECT 
            order_id,
            customer_name,
            product_code,
            quantity,
            order_date,
            delivery_date,
            status,
            priority
        FROM sales_orders
        """
        
        if status:
            query = base_query + " WHERE status = :status ORDER BY order_date DESC"
            params = {"status": status}
        else:
            query = base_query + " ORDER BY order_date DESC"
            params = None
        
        return await self.query_erp_data(query, params)
    
    async def get_mes_equipment_status(self) -> List[Dict[str, Any]]:
        """获取MES设备状态"""
        query = """
        SELECT 
            equipment_code,
            equipment_name,
            status,
            current_efficiency,
            last_maintenance_date,
            next_maintenance_date,
            location
        FROM equipment_status
        ORDER BY equipment_code
        """
        return await self.query_mes_data(query)
    
    async def get_database_status(self) -> Dict[str, Any]:
        """获取所有数据库状态"""
        status = {}
        
        for db_type in DatabaseType:
            if self.db_manager.is_database_configured(db_type):
                try:
                    health = await self.db_manager.health_check(db_type)
                    conn_info = await self.db_manager.get_connection_info(db_type)
                    status[db_type.value] = {
                        "configured": True,
                        "healthy": health,
                        "connection_info": conn_info
                    }
                except Exception as e:
                    status[db_type.value] = {
                        "configured": True,
                        "healthy": False,
                        "error": str(e)
                    }
            else:
                status[db_type.value] = {
                    "configured": False,
                    "healthy": None
                }
        
        return status
    
    async def execute_custom_query(self, db_type: str, query: str, params: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """执行自定义查询"""
        try:
            # 验证数据库类型
            db_enum = DatabaseType(db_type)
            return await self._execute_query(db_enum, query, params)
        except ValueError:
            raise ValueError(f"不支持的数据库类型: {db_type}")


class InventoryQueryService:
    """库存查询服务"""
    
    def __init__(self):
        self.db_service = DatabaseQueryService()
    
    async def get_low_stock_items(self, threshold: int = 100) -> List[Dict[str, Any]]:
        """获取低库存商品"""
        query = """
        SELECT 
            item_code,
            item_name,
            SUM(quantity) as total_quantity,
            unit,
            :threshold as threshold
        FROM inventory 
        WHERE status = 'available'
        GROUP BY item_code, item_name, unit
        HAVING SUM(quantity) < :threshold
        ORDER BY total_quantity ASC
        """
        params = {"threshold": threshold}
        return await self.db_service.query_inventory_data(query, params)
    
    async def get_expiring_items(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取即将过期的商品"""
        query = """
        SELECT 
            item_code,
            item_name,
            quantity,
            expiry_date,
            DATEDIFF(expiry_date, CURDATE()) as days_to_expire
        FROM inventory 
        WHERE status = 'available' 
        AND expiry_date IS NOT NULL
        AND DATEDIFF(expiry_date, CURDATE()) <= :days
        ORDER BY days_to_expire ASC
        """
        params = {"days": days}
        return await self.db_service.query_inventory_data(query, params)


class ProductionAnalysisQueryService:
    """生产数据分析查询服务"""
    
    def __init__(self):
        self.db_service = DatabaseQueryService()
    
    async def get_efficiency_trends(self, production_line: str, days: int = 30) -> List[Dict[str, Any]]:
        """获取效率趋势"""
        query = """
        SELECT 
            DATE(production_date) as date,
            AVG(efficiency) as avg_efficiency,
            MIN(efficiency) as min_efficiency,
            MAX(efficiency) as max_efficiency,
            COUNT(*) as batch_count
        FROM production_records 
        WHERE production_line = :production_line
        AND production_date >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
        GROUP BY DATE(production_date)
        ORDER BY date ASC
        """
        params = {"production_line": production_line, "days": days}
        return await self.db_service.query_production_analysis_data(query, params)
    
    async def get_quality_metrics(self, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取质量指标"""
        query = """
        SELECT 
            production_line,
            product_code,
            AVG(quality_score) as avg_quality,
            SUM(defect_count) as total_defects,
            SUM(output_quantity) as total_output,
            (SUM(defect_count) / SUM(output_quantity) * 100) as defect_rate
        FROM production_records 
        WHERE production_date BETWEEN :start_date AND :end_date
        GROUP BY production_line, product_code
        ORDER BY defect_rate ASC
        """
        params = {"start_date": start_date, "end_date": end_date}
        return await self.db_service.query_production_analysis_data(query, params)


# 全局服务实例
database_query_service = DatabaseQueryService()
inventory_query_service = InventoryQueryService()
production_analysis_query_service = ProductionAnalysisQueryService()
