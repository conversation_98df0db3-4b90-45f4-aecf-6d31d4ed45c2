"""
邮件配置页面
提供邮件服务器配置、测试连接、发送测试邮件等功能
"""

import streamlit as st
import requests
import json
from typing import Dict, Any

# 页面配置
st.set_page_config(
    page_title="邮件配置 - Smart APS",
    page_icon="📧",
    layout="wide"
)

# 应用自定义样式
from config.theme import apply_custom_css
apply_custom_css()

# 导入配置
from config.settings import API_BASE_URL

def get_email_config() -> Dict[str, Any]:
    """获取当前邮件配置"""
    try:
        response = requests.get(f"{API_BASE_URL}/email/config")
        if response.status_code == 200:
            return response.json()
        else:
            st.error(f"获取邮件配置失败: {response.text}")
            return {}
    except Exception as e:
        st.error(f"获取邮件配置失败: {str(e)}")
        return {}

def update_email_config(config: Dict[str, Any]) -> bool:
    """更新邮件配置"""
    try:
        response = requests.post(f"{API_BASE_URL}/email/config", json=config)
        if response.status_code == 200:
            return True
        else:
            st.error(f"更新邮件配置失败: {response.text}")
            return False
    except Exception as e:
        st.error(f"更新邮件配置失败: {str(e)}")
        return False

def test_email_connection() -> Dict[str, Any]:
    """测试邮件连接"""
    try:
        response = requests.get(f"{API_BASE_URL}/email/test")
        if response.status_code == 200:
            return response.json()
        else:
            return {"success": False, "message": response.text}
    except Exception as e:
        return {"success": False, "message": str(e)}

def send_test_email(to_email: str) -> bool:
    """发送测试邮件"""
    try:
        test_data = {
            "to_emails": [to_email],
            "subject": "Smart APS 邮件测试",
            "body": "这是一封来自 Smart APS 系统的测试邮件。如果您收到此邮件，说明邮件配置正常。",
            "html_body": """
            <html>
            <body>
                <h2>📧 Smart APS 邮件测试</h2>
                <p>这是一封来自 Smart APS 系统的测试邮件。</p>
                <p>如果您收到此邮件，说明邮件配置正常。</p>
                <hr>
                <p><small>此邮件由 Smart APS 系统自动发送</small></p>
            </body>
            </html>
            """
        }
        
        response = requests.post(f"{API_BASE_URL}/email/send", json=test_data)
        if response.status_code == 200:
            return True
        else:
            st.error(f"发送测试邮件失败: {response.text}")
            return False
    except Exception as e:
        st.error(f"发送测试邮件失败: {str(e)}")
        return False

# 页面标题
st.title("📧 邮件配置")
st.markdown("---")

# 获取当前配置
current_config = get_email_config()

# 创建两列布局
col1, col2 = st.columns([2, 1])

with col1:
    st.subheader("📋 邮件服务器配置")
    
    with st.form("email_config_form"):
        # SMTP服务器配置
        smtp_host = st.text_input(
            "SMTP服务器地址",
            value=current_config.get("smtp_host", "smtp.gmail.com"),
            help="邮件服务器的SMTP地址"
        )
        
        smtp_port = st.number_input(
            "SMTP端口",
            min_value=1,
            max_value=65535,
            value=current_config.get("smtp_port", 587),
            help="SMTP服务器端口，通常为587(TLS)或465(SSL)"
        )
        
        # 认证信息
        smtp_user = st.text_input(
            "用户名/邮箱地址",
            value=current_config.get("smtp_user", ""),
            help="用于SMTP认证的用户名或邮箱地址"
        )
        
        smtp_password = st.text_input(
            "密码/应用专用密码",
            type="password",
            help="SMTP认证密码或应用专用密码"
        )
        
        # 安全设置
        col_tls, col_ssl = st.columns(2)
        with col_tls:
            smtp_tls = st.checkbox(
                "启用TLS",
                value=current_config.get("smtp_tls", True),
                help="启用TLS加密连接"
            )
        
        with col_ssl:
            smtp_ssl = st.checkbox(
                "启用SSL",
                value=current_config.get("smtp_ssl", False),
                help="启用SSL加密连接"
            )
        
        # 发件人信息
        email_from = st.text_input(
            "发件人显示名称",
            value=current_config.get("email_from", "Smart APS <<EMAIL>>"),
            help="邮件发件人显示的名称和地址"
        )
        
        # 启用状态
        enabled = st.checkbox(
            "启用邮件功能",
            value=current_config.get("enabled", False),
            help="是否启用系统邮件功能"
        )
        
        # 提交按钮
        submitted = st.form_submit_button("💾 保存配置", type="primary")
        
        if submitted:
            config_data = {
                "smtp_host": smtp_host,
                "smtp_port": smtp_port,
                "smtp_user": smtp_user,
                "smtp_password": smtp_password,
                "smtp_tls": smtp_tls,
                "smtp_ssl": smtp_ssl,
                "email_from": email_from,
                "enabled": enabled
            }
            
            if update_email_config(config_data):
                st.success("✅ 邮件配置保存成功！")
                st.rerun()

with col2:
    st.subheader("🔧 测试与验证")
    
    # 连接测试
    if st.button("🔗 测试连接", type="secondary", use_container_width=True):
        with st.spinner("正在测试连接..."):
            result = test_email_connection()
            if result["success"]:
                st.success("✅ 邮件服务器连接成功！")
            else:
                st.error(f"❌ 连接失败: {result['message']}")
    
    st.markdown("---")
    
    # 测试邮件发送
    st.subheader("📤 发送测试邮件")
    test_email = st.text_input(
        "测试邮箱地址",
        placeholder="<EMAIL>",
        help="输入要接收测试邮件的邮箱地址"
    )
    
    if st.button("📧 发送测试邮件", type="secondary", use_container_width=True):
        if test_email:
            with st.spinner("正在发送测试邮件..."):
                if send_test_email(test_email):
                    st.success("✅ 测试邮件发送成功！")
                else:
                    st.error("❌ 测试邮件发送失败")
        else:
            st.warning("请输入测试邮箱地址")

# 邮件模板信息
st.markdown("---")
st.subheader("📝 邮件模板")

# 获取邮件模板列表
try:
    response = requests.get(f"{API_BASE_URL}/email/templates")
    if response.status_code == 200:
        templates = response.json().get("templates", [])
        
        # 显示模板列表
        for template in templates:
            with st.expander(f"📄 {template['name']} ({template['type']})"):
                st.write(template['description'])
    else:
        st.error("获取邮件模板失败")
except Exception as e:
    st.error(f"获取邮件模板失败: {str(e)}")

# 使用说明
st.markdown("---")
st.subheader("📖 使用说明")

st.markdown("""
### 常用邮件服务器配置

#### Gmail
- **SMTP服务器**: smtp.gmail.com
- **端口**: 587 (TLS) 或 465 (SSL)
- **用户名**: 您的Gmail地址
- **密码**: 应用专用密码（需要开启两步验证）

#### Outlook/Hotmail
- **SMTP服务器**: smtp-mail.outlook.com
- **端口**: 587 (TLS)
- **用户名**: 您的Outlook邮箱地址
- **密码**: 您的邮箱密码

#### 企业邮箱
- 请联系您的IT管理员获取SMTP配置信息

### 安全提示
- 建议使用应用专用密码而不是邮箱登录密码
- 确保SMTP连接使用TLS或SSL加密
- 定期更换邮箱密码和应用专用密码
""")

# 页脚
st.markdown("---")
st.markdown(
    "<div style='text-align: center; color: #666; font-size: 14px;'>"
    "Smart APS 邮件配置 | 支持多种邮件服务商"
    "</div>",
    unsafe_allow_html=True
)
