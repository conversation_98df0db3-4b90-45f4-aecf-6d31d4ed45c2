# 🗄️ Smart APS 数据库配置指南

## 📋 概述

Smart APS 支持两种数据库配置方式：
1. **环境变量配置** - 通过 .env 文件配置（后台实现）
2. **前端界面配置** - 通过Web界面可视化配置（前端界面）

## 🔧 配置方式对比

| 配置方式 | 优点 | 缺点 | 适用场景 |
|---------|------|------|----------|
| **环境变量配置** | 安全性高、启动时加载、版本控制友好 | 需要重启服务、技术门槛高 | 生产环境、固定配置 |
| **前端界面配置** | 用户友好、实时生效、无需重启 | 需要权限控制、配置存储 | 开发环境、动态配置 |

## 🌐 前端界面配置

### 1. 访问配置页面

通过Smart APS前端界面访问：
- **页面路径**: `/数据库配置`
- **权限要求**: 管理员或有数据库配置权限的用户
- **功能**: 可视化配置、测试连接、健康检查

### 2. 配置操作

#### 📝 添加数据库配置

1. **选择操作**: 点击"添加配置"
2. **填写信息**:
   - 数据库名称: 唯一标识符
   - 数据库类型: MySQL、Oracle、PostgreSQL等
   - 主机地址: 数据库服务器地址
   - 端口号: 数据库端口
   - 用户名: 数据库用户名
   - 密码: 数据库密码
   - 数据库名: 要连接的数据库
   - 描述: 配置说明

3. **高级选项**:
   - 自定义连接字符串
   - 额外参数配置

4. **测试连接**: 验证配置是否正确
5. **保存配置**: 创建数据库配置

#### ✏️ 编辑数据库配置

1. **选择配置**: 从配置列表选择要编辑的配置
2. **修改信息**: 更新配置参数
3. **测试连接**: 验证修改后的配置
4. **保存更改**: 更新配置

#### 🗑️ 删除数据库配置

1. **选择配置**: 选择要删除的配置
2. **确认删除**: 确认删除操作
3. **注意**: 主数据库配置不能删除

#### 🔍 测试连接

1. **连接测试**: 验证数据库连接是否正常
2. **健康检查**: 检查已配置数据库的健康状态
3. **批量检查**: 一次性检查所有数据库

### 3. 界面功能

```python
# 前端配置界面功能
- 📊 配置列表展示
- ➕ 添加新配置
- ✏️ 编辑现有配置
- 🗑️ 删除配置
- 🔍 测试连接
- 🏥 健康检查
- 📋 配置模板
- 🔄 实时状态更新
```

## 💻 环境变量配置

### 1. .env文件配置

```bash
# =============================================================================
# 主数据库配置 (必需)
# =============================================================================
DATABASE_URL=mysql+aiomysql://smart_aps:password@localhost:3306/smart_aps?charset=utf8mb4
DB_HOST=localhost
DB_PORT=3306
DB_USER=smart_aps
DB_PASSWORD=smart_aps_password
DB_NAME=smart_aps

# =============================================================================
# PCI数据库配置 (可选)
# =============================================================================
PCI_DB_URL=mysql+aiomysql://pci_user:password@localhost:3306/pci_system?charset=utf8mb4
PCI_DB_HOST=localhost
PCI_DB_PORT=3306
PCI_DB_USER=pci_user
PCI_DB_PASSWORD=pci_password
PCI_DB_NAME=pci_system
PCI_DB_ENGINE=mysql

# =============================================================================
# Oracle数据库配置 (可选)
# =============================================================================
ORACLE_DB_URL=oracle+oracledb://oracle_user:password@localhost:1521/?service_name=ORCL
ORACLE_DB_HOST=localhost
ORACLE_DB_PORT=1521
ORACLE_DB_USER=oracle_user
ORACLE_DB_PASSWORD=oracle_password
ORACLE_DB_NAME=ORCL
ORACLE_DB_ENGINE=oracle

# =============================================================================
# 自定义数据库配置 (可选)
# =============================================================================
CUSTOM1_DB_URL=postgresql+asyncpg://custom1_user:password@localhost:5432/custom1_db
CUSTOM1_DB_ENGINE=postgresql

CUSTOM2_DB_URL=mssql+aioodbc://custom2_user:password@localhost:1433/custom2_db
CUSTOM2_DB_ENGINE=sqlserver
```

### 2. 配置规则

- **完整URL优先**: 如果设置了 `*_DB_URL`，优先使用完整URL
- **分项配置**: 如果没有完整URL，根据分项参数自动构建
- **引擎类型**: 通过 `*_DB_ENGINE` 指定数据库引擎类型
- **可选配置**: 除主数据库外，其他都是可选的

## 🔄 配置生效方式

### 前端界面配置
- **实时生效**: 配置后立即可用
- **动态加载**: 无需重启服务
- **持久化存储**: 配置保存到主数据库
- **权限控制**: 需要相应权限才能配置

### 环境变量配置
- **启动时加载**: 服务启动时读取
- **需要重启**: 修改后需重启服务生效
- **文件存储**: 配置保存在.env文件
- **版本控制**: 可纳入版本控制管理

## 🔒 安全考虑

### 1. 密码安全
```python
# 前端配置
- 密码输入框使用password类型
- 传输过程使用HTTPS加密
- 存储时进行加密处理

# 环境变量配置
- .env文件不纳入版本控制
- 生产环境使用密钥管理服务
- 定期更换数据库密码
```

### 2. 权限控制
```python
# 前端权限
- 只有管理员可以配置数据库
- 操作日志记录
- 配置变更审计

# 数据库权限
- 使用最小权限原则
- 为每个应用创建专用用户
- 限制网络访问
```

## 📊 配置管理最佳实践

### 1. 配置分类

```python
# 按环境分类
development_configs = {
    "inventory_dev": "开发环境库存数据库",
    "pci_dev": "开发环境PCI数据库"
}

production_configs = {
    "inventory_prod": "生产环境库存数据库",
    "pci_prod": "生产环境PCI数据库"
}

# 按功能分类
functional_configs = {
    "inventory": "库存管理",
    "production": "生产管理", 
    "quality": "质量管理",
    "logistics": "物流管理"
}
```

### 2. 配置模板

```python
# MySQL模板
mysql_template = {
    "engine_type": "mysql",
    "port": 3306,
    "extra_params": {"charset": "utf8mb4"}
}

# Oracle模板
oracle_template = {
    "engine_type": "oracle",
    "port": 1521,
    "extra_params": {"service_name": "ORCL"}
}

# PostgreSQL模板
postgresql_template = {
    "engine_type": "postgresql", 
    "port": 5432,
    "extra_params": {}
}
```

### 3. 配置验证

```python
# 配置验证规则
validation_rules = {
    "name": "必填，唯一，字母数字下划线",
    "host": "必填，有效的主机地址",
    "port": "必填，1-65535范围",
    "username": "必填，有效的用户名",
    "password": "必填，强密码策略",
    "database": "必填，有效的数据库名"
}
```

## 🚀 使用示例

### 1. 前端界面配置PCI数据库

```python
# 步骤1: 访问配置页面
# 导航到 "数据库配置" 页面

# 步骤2: 添加PCI数据库配置
config = {
    "name": "pci_database",
    "engine_type": "mysql",
    "host": "*************",
    "port": 3306,
    "username": "pci_user",
    "password": "secure_password",
    "database": "pci_system",
    "description": "PCI数据管理系统"
}

# 步骤3: 测试连接
# 点击"测试连接"按钮验证配置

# 步骤4: 保存配置
# 点击"创建配置"保存
```

### 2. 环境变量配置Oracle数据库

```bash
# .env文件配置
ORACLE_DB_URL=oracle+oracledb://oracle_user:oracle_password@*************:1521/?service_name=ORCL
ORACLE_DB_HOST=*************
ORACLE_DB_PORT=1521
ORACLE_DB_USER=oracle_user
ORACLE_DB_PASSWORD=oracle_password
ORACLE_DB_NAME=ORCL
ORACLE_DB_ENGINE=oracle
```

### 3. 使用配置的数据库

```python
# Python代码中使用
from app.services.database_query_service import database_query_service

# 查询PCI数据
pci_data = await database_query_service.query_pci_data(
    "SELECT * FROM pci_items WHERE age_days > :days",
    {"days": 180}
)

# 查询Oracle数据
oracle_data = await database_query_service.query_oracle_data(
    "SELECT * FROM inventory WHERE status = :status",
    {"status": "ACTIVE"}
)
```

## 🎯 总结

**✅ 两种配置方式各有优势：**

1. **前端界面配置**:
   - 👍 用户友好，可视化操作
   - 👍 实时生效，无需重启
   - 👍 测试连接，即时验证
   - 👍 权限控制，安全可靠

2. **环境变量配置**:
   - 👍 安全性高，文件管理
   - 👍 版本控制，配置追踪
   - 👍 批量配置，启动加载
   - 👍 生产环境，稳定可靠

**🎯 建议使用场景：**
- **开发环境**: 优先使用前端界面配置，方便调试
- **测试环境**: 两种方式结合使用
- **生产环境**: 优先使用环境变量配置，确保安全

**🚀 Smart APS 提供了灵活的数据库配置方案，满足不同场景的需求！**
