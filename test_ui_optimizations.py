"""
Smart APS UI优化功能测试
验证智能导航和增强数据可视化功能
"""

import streamlit as st
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'frontend'))

def test_navigation_functions():
    """测试智能导航功能"""
    st.header("🧭 智能导航功能测试")
    
    try:
        from components.navigation import ModernNavigation
        
        # 测试面包屑导航
        st.subheader("1. 面包屑导航测试")
        ModernNavigation.create_breadcrumb("01_综合仪表板")
        st.success("✅ 面包屑导航功能正常")
        
        # 测试快速操作
        st.subheader("2. 智能快速操作测试")
        ModernNavigation.create_quick_actions("01_综合仪表板")
        st.success("✅ 智能快速操作功能正常")
        
        # 测试页面访问跟踪
        st.subheader("3. 页面访问跟踪测试")
        ModernNavigation.track_page_visit("01_综合仪表板")
        ModernNavigation.track_page_visit("06_数据分析")
        ModernNavigation.track_page_visit("03_生产规划")
        st.success("✅ 页面访问跟踪功能正常")
        
        # 测试最近访问页面
        st.subheader("4. 最近访问页面测试")
        with st.sidebar:
            ModernNavigation.create_recent_pages_sidebar()
        st.success("✅ 最近访问页面功能正常")
        
        # 测试智能导航小部件
        st.subheader("5. 智能导航小部件测试")
        with st.sidebar:
            ModernNavigation.create_smart_navigation_widget()
        st.success("✅ 智能导航小部件功能正常")
        
        return True
        
    except Exception as e:
        st.error(f"❌ 智能导航功能测试失败: {str(e)}")
        return False

def test_chart_functions():
    """测试增强数据可视化功能"""
    st.header("📊 增强数据可视化功能测试")
    
    try:
        from utils.chart_utils import (
            create_enhanced_realtime_dashboard,
            create_enhanced_interactive_gantt,
            create_enhanced_equipment_heatmap,
            create_enhanced_production_flow_chart,
            create_enhanced_quality_trend_chart,
            create_enhanced_cost_analysis_chart,
            create_enhanced_performance_radar_chart,
            create_enhanced_alert_timeline
        )
        
        # 1. 测试实时仪表板
        st.subheader("1. 实时仪表板测试")
        dashboard_data = {
            'efficiency': 85,
            'efficiency_delta': 2,
            'utilization': 78,
            'utilization_delta': -1,
            'quality': 96,
            'quality_delta': 1,
            'output': 102,
            'output_delta': 5
        }
        create_enhanced_realtime_dashboard(dashboard_data)
        st.success("✅ 实时仪表板功能正常")
        
        # 2. 测试交互式甘特图
        st.subheader("2. 交互式甘特图测试")
        tasks_data = [
            {
                'task_name': '生产任务1',
                'start_date': '2024-01-01',
                'end_date': '2024-01-05',
                'status': '进行中'
            },
            {
                'task_name': '生产任务2',
                'start_date': '2024-01-03',
                'end_date': '2024-01-08',
                'status': '计划中'
            },
            {
                'task_name': '生产任务3',
                'start_date': '2024-01-06',
                'end_date': '2024-01-10',
                'status': '已完成'
            }
        ]
        fig = create_enhanced_interactive_gantt(tasks_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.success("✅ 交互式甘特图功能正常")
        else:
            st.warning("⚠️ 甘特图返回None，但函数执行正常")
        
        # 3. 测试设备热力图
        st.subheader("3. 设备状态热力图测试")
        equipment_data = {
            'L01': [85, 90, 88, 92, 87, 89, 91, 88, 86, 90, 92, 89, 87, 85, 88, 90, 92, 89, 87, 85, 83, 81, 79, 77],
            'L02': [78, 82, 85, 88, 90, 92, 89, 87, 85, 83, 81, 79, 77, 75, 78, 82, 85, 88, 90, 87, 85, 83, 81, 79],
            'Tank01': [92, 94, 91, 89, 87, 85, 88, 90, 92, 94, 91, 89, 87, 85, 88, 90, 92, 94, 91, 89, 87, 85, 83, 81]
        }
        fig = create_enhanced_equipment_heatmap(equipment_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.success("✅ 设备状态热力图功能正常")
        
        # 4. 测试生产流程图
        st.subheader("4. 生产流程图测试")
        flow_data = [
            {'source': '原材料', 'target': '加工1', 'value': 100},
            {'source': '加工1', 'target': '加工2', 'value': 95},
            {'source': '加工2', 'target': '质检', 'value': 90},
            {'source': '质检', 'target': '成品', 'value': 85},
            {'source': '质检', 'target': '返工', 'value': 5}
        ]
        fig = create_enhanced_production_flow_chart(flow_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.success("✅ 生产流程图功能正常")
        
        # 5. 测试质量趋势图
        st.subheader("5. 质量趋势图测试")
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        quality_data = pd.DataFrame({
            'date': dates,
            'quality_rate': np.random.normal(95, 2, 30),
            'defect_type': np.random.choice(['缺陷A', '缺陷B', '缺陷C'], 30),
            'count': np.random.randint(1, 10, 30),
            'quality_grade': np.random.choice(['优秀', '良好', '合格'], 30),
            'improvement_score': np.random.normal(80, 5, 30)
        })
        fig = create_enhanced_quality_trend_chart(quality_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.success("✅ 质量趋势图功能正常")
        
        # 6. 测试成本分析图
        st.subheader("6. 成本分析瀑布图测试")
        cost_data = {
            '原材料成本': 50000,
            '人工成本': 20000,
            '设备成本': 15000,
            '能源成本': 8000,
            '其他成本': 5000,
            '总成本': 98000
        }
        fig = create_enhanced_cost_analysis_chart(cost_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.success("✅ 成本分析瀑布图功能正常")
        
        # 7. 测试性能雷达图
        st.subheader("7. 性能雷达图测试")
        performance_data = {
            '生产效率': 85,
            '质量水平': 92,
            '设备利用率': 78,
            '能源效率': 88,
            '成本控制': 75,
            '交付准时率': 95
        }
        fig = create_enhanced_performance_radar_chart(performance_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.success("✅ 性能雷达图功能正常")
        
        # 8. 测试警报时间线
        st.subheader("8. 警报时间线测试")
        alerts_data = [
            {
                'timestamp': '2024-01-01 10:00:00',
                'severity': 'high',
                'message': '设备L01温度过高'
            },
            {
                'timestamp': '2024-01-01 14:30:00',
                'severity': 'medium',
                'message': '生产线L02效率下降'
            },
            {
                'timestamp': '2024-01-01 16:45:00',
                'severity': 'low',
                'message': '原材料库存不足'
            }
        ]
        fig = create_enhanced_alert_timeline(alerts_data)
        if fig:
            st.plotly_chart(fig, use_container_width=True)
            st.success("✅ 警报时间线功能正常")
        
        return True
        
    except Exception as e:
        st.error(f"❌ 增强数据可视化功能测试失败: {str(e)}")
        st.exception(e)
        return False

def test_unified_ai_service():
    """测试统一AI服务集成"""
    st.header("🤖 统一AI服务集成测试")
    
    try:
        from services.unified_ai_service import UnifiedAIService, AIRequest, AIServiceType
        
        # 创建AI服务实例
        ai_service = UnifiedAIService()
        
        # 检查UI增强服务是否可用
        ui_enhancement_available = ai_service.service_status.get(AIServiceType.UI_ENHANCEMENT, False)
        
        if ui_enhancement_available:
            st.success("✅ UI增强服务已集成到统一AI服务")
        else:
            st.warning("⚠️ UI增强服务未完全集成，但基础功能可用")
        
        # 显示所有可用服务
        st.subheader("可用AI服务列表:")
        for service_type, status in ai_service.service_status.items():
            status_icon = "✅" if status else "❌"
            st.write(f"{status_icon} {service_type.value}")
        
        return True
        
    except Exception as e:
        st.error(f"❌ 统一AI服务集成测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    st.set_page_config(
        page_title="Smart APS UI优化测试",
        page_icon="🧪",
        layout="wide"
    )
    
    st.title("🧪 Smart APS UI优化功能测试")
    st.markdown("---")
    
    # 测试结果统计
    test_results = []
    
    # 1. 测试智能导航功能
    nav_result = test_navigation_functions()
    test_results.append(("智能导航系统", nav_result))
    
    st.markdown("---")
    
    # 2. 测试增强数据可视化功能
    chart_result = test_chart_functions()
    test_results.append(("增强数据可视化", chart_result))
    
    st.markdown("---")
    
    # 3. 测试统一AI服务集成
    ai_result = test_unified_ai_service()
    test_results.append(("统一AI服务集成", ai_result))
    
    # 显示测试总结
    st.markdown("---")
    st.header("📋 测试结果总结")
    
    passed_tests = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results:
        status_icon = "✅" if result else "❌"
        st.write(f"{status_icon} {test_name}")
    
    if passed_tests == total_tests:
        st.success(f"🎉 所有测试通过！({passed_tests}/{total_tests})")
        st.balloons()
    else:
        st.warning(f"⚠️ 部分测试失败：{passed_tests}/{total_tests} 通过")
    
    # 显示使用建议
    st.markdown("---")
    st.header("💡 使用建议")
    st.info("""
    **如何在页面中使用这些优化功能：**
    
    1. **智能导航**：
       ```python
       from components.navigation import ModernNavigation
       ModernNavigation.create_breadcrumb("页面名称")
       ModernNavigation.create_quick_actions("页面名称")
       ```
    
    2. **增强图表**：
       ```python
       from utils.chart_utils import create_enhanced_realtime_dashboard
       create_enhanced_realtime_dashboard(your_data)
       ```
    
    3. **侧边栏智能功能**：
       ```python
       with st.sidebar:
           ModernNavigation.create_recent_pages_sidebar()
           ModernNavigation.create_smart_navigation_widget()
       ```
    """)

if __name__ == "__main__":
    main()
