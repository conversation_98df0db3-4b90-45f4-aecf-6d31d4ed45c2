"""
增强图表组件
提供更直观、交互性更强的数据可视化
"""

import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

class EnhancedCharts:
    """增强图表组件"""
    
    @staticmethod
    def create_realtime_dashboard(data: Dict[str, Any]):
        """创建实时仪表板"""
        st.markdown("### 📊 实时生产监控")
        
        # 创建指标卡片
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="🏭 生产效率",
                value=f"{data.get('efficiency', 85)}%",
                delta=f"{data.get('efficiency_delta', 2)}%"
            )
        
        with col2:
            st.metric(
                label="⚙️ 设备利用率",
                value=f"{data.get('utilization', 78)}%",
                delta=f"{data.get('utilization_delta', -1)}%"
            )
        
        with col3:
            st.metric(
                label="✅ 质量合格率",
                value=f"{data.get('quality', 96)}%",
                delta=f"{data.get('quality_delta', 1)}%"
            )
        
        with col4:
            st.metric(
                label="📦 产量完成率",
                value=f"{data.get('output', 102)}%",
                delta=f"{data.get('output_delta', 5)}%"
            )
    
    @staticmethod
    def create_interactive_gantt(tasks_data: List[Dict[str, Any]]):
        """创建交互式甘特图"""
        if not tasks_data:
            st.warning("暂无任务数据")
            return
        
        df = pd.DataFrame(tasks_data)
        
        # 创建甘特图
        fig = px.timeline(
            df,
            x_start="start_date",
            x_end="end_date",
            y="task_name",
            color="status",
            title="📅 生产计划甘特图",
            color_discrete_map={
                "进行中": "#4CAF50",
                "计划中": "#2196F3",
                "已完成": "#9E9E9E",
                "延期": "#F44336"
            }
        )
        
        fig.update_layout(
            height=400,
            xaxis_title="时间",
            yaxis_title="任务",
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def create_equipment_heatmap(equipment_data: Dict[str, List[float]]):
        """创建设备状态热力图"""
        if not equipment_data:
            st.warning("暂无设备数据")
            return
        
        # 准备数据
        equipment_names = list(equipment_data.keys())
        hours = list(range(24))
        
        # 创建热力图数据
        z_data = []
        for equipment in equipment_names:
            z_data.append(equipment_data[equipment])
        
        fig = go.Figure(data=go.Heatmap(
            z=z_data,
            x=[f"{h:02d}:00" for h in hours],
            y=equipment_names,
            colorscale='RdYlGn',
            reversescale=True,
            colorbar=dict(title="利用率 %")
        ))
        
        fig.update_layout(
            title="🔥 设备24小时利用率热力图",
            xaxis_title="时间",
            yaxis_title="设备",
            height=300
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def create_production_flow_chart(flow_data: List[Dict[str, Any]]):
        """创建生产流程图"""
        if not flow_data:
            st.warning("暂无流程数据")
            return
        
        # 创建桑基图
        labels = []
        sources = []
        targets = []
        values = []
        
        for item in flow_data:
            if item['source'] not in labels:
                labels.append(item['source'])
            if item['target'] not in labels:
                labels.append(item['target'])
            
            sources.append(labels.index(item['source']))
            targets.append(labels.index(item['target']))
            values.append(item['value'])
        
        fig = go.Figure(data=[go.Sankey(
            node=dict(
                pad=15,
                thickness=20,
                line=dict(color="black", width=0.5),
                label=labels,
                color="blue"
            ),
            link=dict(
                source=sources,
                target=targets,
                value=values
            )
        )])
        
        fig.update_layout(
            title_text="🔄 生产流程图",
            font_size=10,
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def create_quality_trend_chart(quality_data: pd.DataFrame):
        """创建质量趋势图"""
        if quality_data.empty:
            st.warning("暂无质量数据")
            return
        
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('质量指标趋势', '缺陷分布'),
            vertical_spacing=0.1
        )
        
        # 质量趋势线
        fig.add_trace(
            go.Scatter(
                x=quality_data['date'],
                y=quality_data['quality_rate'],
                mode='lines+markers',
                name='质量合格率',
                line=dict(color='#4CAF50', width=3)
            ),
            row=1, col=1
        )
        
        # 缺陷分布饼图
        defect_data = quality_data.groupby('defect_type')['count'].sum()
        fig.add_trace(
            go.Pie(
                labels=defect_data.index,
                values=defect_data.values,
                name="缺陷分布"
            ),
            row=2, col=1
        )
        
        fig.update_layout(
            height=600,
            showlegend=True,
            title_text="📈 质量分析报告"
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def create_cost_analysis_chart(cost_data: Dict[str, float]):
        """创建成本分析图"""
        if not cost_data:
            st.warning("暂无成本数据")
            return
        
        categories = list(cost_data.keys())
        values = list(cost_data.values())
        
        # 创建瀑布图
        fig = go.Figure(go.Waterfall(
            name="成本分析",
            orientation="v",
            measure=["relative"] * (len(categories) - 1) + ["total"],
            x=categories,
            textposition="outside",
            text=[f"¥{v:,.0f}" for v in values],
            y=values,
            connector={"line": {"color": "rgb(63, 63, 63)"}},
        ))
        
        fig.update_layout(
            title="💰 成本分析瀑布图",
            showlegend=False,
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def create_performance_radar_chart(performance_data: Dict[str, float]):
        """创建性能雷达图"""
        if not performance_data:
            st.warning("暂无性能数据")
            return
        
        categories = list(performance_data.keys())
        values = list(performance_data.values())
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=values,
            theta=categories,
            fill='toself',
            name='当前性能',
            line_color='#4A90E2'
        ))
        
        # 添加目标线
        target_values = [90] * len(categories)  # 假设目标都是90%
        fig.add_trace(go.Scatterpolar(
            r=target_values,
            theta=categories,
            fill='toself',
            name='目标性能',
            line_color='#50C878',
            opacity=0.3
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, 100]
                )),
            showlegend=True,
            title="🎯 综合性能雷达图",
            height=400
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    @staticmethod
    def create_alert_timeline(alerts_data: List[Dict[str, Any]]):
        """创建警报时间线"""
        if not alerts_data:
            st.info("🎉 暂无警报，系统运行正常")
            return
        
        df = pd.DataFrame(alerts_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 按严重程度分组
        severity_colors = {
            'high': '#F44336',
            'medium': '#FF9800',
            'low': '#4CAF50'
        }
        
        fig = go.Figure()
        
        for severity in ['high', 'medium', 'low']:
            severity_data = df[df['severity'] == severity]
            if not severity_data.empty:
                fig.add_trace(go.Scatter(
                    x=severity_data['timestamp'],
                    y=[severity] * len(severity_data),
                    mode='markers',
                    marker=dict(
                        size=10,
                        color=severity_colors[severity]
                    ),
                    name=f'{severity.upper()} 级警报',
                    text=severity_data['message'],
                    hovertemplate='<b>%{text}</b><br>时间: %{x}<extra></extra>'
                ))
        
        fig.update_layout(
            title="⚠️ 警报时间线",
            xaxis_title="时间",
            yaxis_title="严重程度",
            height=300,
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
