"""
简单的功能测试
"""

def test_basic_imports():
    """测试基本导入"""
    print("🔍 测试基本导入...")
    
    # 测试标准库
    try:
        import streamlit as st
        print("✅ streamlit 导入成功")
    except ImportError as e:
        print(f"❌ streamlit 导入失败: {e}")
        return False
    
    try:
        import plotly.graph_objects as go
        import plotly.express as px
        print("✅ plotly 导入成功")
    except ImportError as e:
        print(f"❌ plotly 导入失败: {e}")
        return False
    
    try:
        import pandas as pd
        import numpy as np
        print("✅ pandas/numpy 导入成功")
    except ImportError as e:
        print(f"❌ pandas/numpy 导入失败: {e}")
        return False
    
    return True

def test_file_existence():
    """测试文件是否存在"""
    print("\n📁 测试文件存在性...")
    
    import os
    
    files_to_check = [
        "frontend/components/navigation.py",
        "frontend/utils/chart_utils.py", 
        "frontend/services/unified_ai_service.py"
    ]
    
    all_exist = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            all_exist = False
    
    return all_exist

def test_syntax():
    """测试语法"""
    print("\n🔍 测试语法...")
    
    import ast
    import os
    
    files_to_check = [
        "frontend/components/navigation.py",
        "frontend/utils/chart_utils.py",
        "frontend/services/unified_ai_service.py"
    ]
    
    all_valid = True
    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                ast.parse(content)
                print(f"✅ {file_path} 语法正确")
            except SyntaxError as e:
                print(f"❌ {file_path} 语法错误: {e}")
                all_valid = False
            except Exception as e:
                print(f"❌ {file_path} 检查失败: {e}")
                all_valid = False
        else:
            print(f"❌ {file_path} 文件不存在")
            all_valid = False
    
    return all_valid

def test_function_definitions():
    """测试函数定义"""
    print("\n🔧 测试函数定义...")
    
    import sys
    import os
    
    # 添加frontend路径
    sys.path.insert(0, os.path.join(os.getcwd(), 'frontend'))
    
    # 测试导航组件
    try:
        import importlib.util
        
        # 加载navigation模块
        spec = importlib.util.spec_from_file_location("navigation", "frontend/components/navigation.py")
        navigation_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(navigation_module)
        
        # 检查ModernNavigation类
        if hasattr(navigation_module, 'ModernNavigation'):
            nav_class = navigation_module.ModernNavigation
            
            required_methods = [
                'create_breadcrumb',
                'create_quick_actions',
                'track_page_visit',
                'create_recent_pages_sidebar',
                'create_smart_navigation_widget'
            ]
            
            for method in required_methods:
                if hasattr(nav_class, method):
                    print(f"✅ ModernNavigation.{method} 存在")
                else:
                    print(f"❌ ModernNavigation.{method} 不存在")
                    return False
        else:
            print("❌ ModernNavigation 类不存在")
            return False
            
        print("✅ 导航组件函数定义正确")
        
    except Exception as e:
        print(f"❌ 导航组件测试失败: {e}")
        return False
    
    # 测试图表工具
    try:
        spec = importlib.util.spec_from_file_location("chart_utils", "frontend/utils/chart_utils.py")
        chart_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(chart_module)
        
        required_functions = [
            'create_enhanced_realtime_dashboard',
            'create_enhanced_interactive_gantt',
            'create_enhanced_equipment_heatmap'
        ]
        
        for func in required_functions:
            if hasattr(chart_module, func):
                print(f"✅ {func} 存在")
            else:
                print(f"❌ {func} 不存在")
                return False
                
        print("✅ 图表工具函数定义正确")
        
    except Exception as e:
        print(f"❌ 图表工具测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🧪 Smart APS UI优化功能简单测试")
    print("=" * 50)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("文件存在性", test_file_existence),
        ("语法检查", test_syntax),
        ("函数定义", test_function_definitions)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}测试:")
        print("-" * 30)
        
        try:
            result = test_func()
            if result:
                print(f"✅ {test_name}测试通过")
                passed += 1
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败")
        return False

if __name__ == "__main__":
    import os
    os.chdir(r"d:\soft\Smart APS")
    success = main()
    exit(0 if success else 1)
