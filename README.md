# 🏭 Smart Planning - 智能生产管理系统

[![Python](https://img.shields.io/badge/Python-3.11+-blue.svg)](https://python.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com)
[![Streamlit](https://img.shields.io/badge/Streamlit-1.28+-red.svg)](https://streamlit.io)
[![MySQL](https://img.shields.io/badge/MySQL-8.0+-blue.svg)](https://mysql.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 基于Industry 4.0标准的智能生产管理系统，专为20-30人团队设计

## 📋 项目简介

Smart Planning（智能生产管理系统）是一个现代化的智能生产管理系统，采用前后端分离架构，集成AI算法、实时数据处理和智能优化引擎，为智能制造工厂提供全面的生产管理解决方案。

### 🎯 核心特性

- **🤖 智能AI助手**: 集成Ollama/Azure OpenAI，支持中英文对话，提供专业生产建议
- **� 邮件表格提取**: 智能提取邮件正文中的表格数据，支持HTML、文本、CSV格式
- **📊 增强数据可视化**: 8种专业图表类型，交互式甘特图、热力图、雷达图等
- **🧭 智能导航系统**: 智能工作流检测、页面访问跟踪、个性化推荐
- **🧮 统一算法中心**: 6种优化算法，支持遗传算法、强化学习、混合算法
- **💾 统一数据中心**: 多源数据集成，支持Excel、邮件、API、MES/ERP系统
- **� 企业级认证**: 支持LDAP、SSO、多用户角色管理
- **🌐 国际化支持**: 中英文双语界面，多时区支持

### 📈 预期效益

| 指标 | 预期提升 | 说明 |
|------|---------|------|
| 生产效率 | 15%-25% | AI优化资源配置，智能调度算法 |
| 计划准确率 | 提升至95%+ | 机器学习预测，持续优化 |
| 响应速度 | 从小时级到分钟级 | 自动化计划生成和实时调整 |
| 数据处理效率 | 提升80% | 智能邮件解析，自动表格提取 |
| 用户体验满意度 | 提升90% | 智能导航，个性化界面 |

## 🏗️ 系统架构

### 前后端分离架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Streamlit)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 智能导航     │ │ 增强图表     │ │ 响应式布局   │ │ UI主题      │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      页面层 (13个核心页面)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 综合仪表板   │ │ 数据上传     │ │ 生产规划     │ │ 设备管理     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 计划监控     │ │ 数据分析     │ │ 智能助手     │ │ PCI管理     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 供应链协同   │ │ 能耗优化     │ │ 算法中心     │ │ 数据中心     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
│  ┌─────────────┐                                                │
│  │ 系统管理     │                                                │
│  └─────────────┘                                                │
├─────────────────────────────────────────────────────────────┤
│                    统一服务层 (4个核心服务)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 统一AI服务   │ │ 统一算法服务 │ │ 统一数据服务 │ │ 统一系统服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    后端API层 (FastAPI)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ 认证授权     │ │ 文件上传     │ │ 数据处理     │ │ 算法执行     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    数据存储层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │ MySQL 8.0   │ │ Redis缓存   │ │ 文件存储     │ │ 配置存储     │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🛠️ 技术栈

### 后端技术

- **Python 3.11+** - 主要开发语言
- **FastAPI** - 高性能异步Web框架
- **SQLAlchemy** - 异步ORM框架
- **MySQL 8.0+** - 主数据库
- **Redis 7.0+** - 缓存和会话管理
- **Pydantic** - 数据验证和序列化

### 前端技术

- **Streamlit** - 快速构建数据应用
- **Plotly** - 交互式数据可视化
- **Pandas** - 数据处理和分析
- **NumPy** - 数值计算

### AI与算法

- **Ollama** - 本地大型语言模型
- **Azure OpenAI** - 云端LLM服务
- **Scikit-learn** - 机器学习算法
- **Optuna** - 超参数优化
- **遗传算法** - 全局优化
- **强化学习** - 智能决策

### 数据处理

- **openpyxl** - Excel文件处理
- **email** - 邮件解析和表格提取
- **BeautifulSoup** - HTML解析
- **python-multipart** - 文件上传

## 🚀 快速开始

### 环境要求

- **Python 3.11+**
- **MySQL 8.0+**
- **Redis 7.0+**
- **Docker 20.10+** 和 **Docker Compose** (可选)
- **4核CPU, 8GB内存** (推荐配置)

### 一键Docker部署

```bash
# 1. 克隆项目
git clone https://github.com/your-org/smart-aps.git
cd smart-aps

# 2. 复制环境配置
cp .env.example .env
# 编辑 .env 文件，配置数据库和其他参数

# 3. 启动所有服务
docker-compose up -d

# 4. 等待服务启动完成（约2-3分钟）
docker-compose logs -f

# 访问地址：
# - 前端界面: http://localhost:8501
# - 后端API: http://localhost:8000
# - API文档: http://localhost:8000/docs
# - 默认账户: admin / admin123456
```

### 前后端分离部署

#### 后端部署

```bash
# 1. 安装后端依赖
cd backend
pip install -r requirements.txt

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置MySQL连接信息

# 3. 初始化数据库
python scripts/init_database.py
python scripts/init_data.py

# 4. 启动后端API服务
chmod +x scripts/start.sh
./scripts/start.sh

# 或者直接使用uvicorn
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

#### 前端部署

```bash
# 1. 安装前端依赖
cd frontend
pip install -r requirements.txt

# 2. 配置后端API地址
# 编辑 config/settings.py 中的 API_BASE_URL

# 3. 启动前端应用
streamlit run main.py --server.port 8501 --server.address 0.0.0.0
```

### Ollama本地LLM配置（可选）

```bash
# 1. 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 2. 下载模型
ollama pull llama2
ollama pull mistral

# 3. 启动Ollama服务
ollama serve

# 4. 配置环境变量
export OLLAMA_BASE_URL=http://localhost:11434
export DEFAULT_LLM_SERVICE=ollama
```

## 📚 文档中心

完整文档请查看 [docs](./docs/) 目录：

### 🏗️ 系统架构文档

- [系统架构总览](./docs/architecture/system_architecture.md) - 系统整体架构设计

### 📖 用户指南

- [快速开始指南](./docs/user_guide/quick_start.md) - 5分钟快速上手
- [功能使用指南](./docs/user_guide/feature_guide.md) - 详细功能使用说明

### 🔧 技术文档

- [开发指南](./docs/technical/development_guide.md) - 开发环境搭建和开发指南
- [扩展开发指南](./docs/technical/extension_development.md) - 插件和扩展开发指南
- [前端开发指南](./docs/technical/frontend_development.md) - Streamlit前端开发
- [技术设计文档](./docs/technical/technical_design.md) - 详细技术设计

## 🎮 核心功能

### 📧 智能邮件解析

- **最新回复提取**: 智能提取邮件最新回复中的表格数据
- **多格式支持**: HTML表格、文本表格、CSV格式
- **智能分离**: 自动分离邮件回复和历史内容
- **数据验证**: 自动验证数据完整性和格式

### 🧭 智能导航系统

- **智能面包屑**: 显示当前位置和导航路径
- **工作流检测**: 智能推荐下一步操作
- **访问跟踪**: 记录页面访问历史和热度
- **个性化推荐**: 基于使用习惯的个性化导航

### 📊 增强数据可视化

- **实时仪表板**: 生产效率、设备利用率监控
- **交互式甘特图**: 生产计划可视化，支持拖拽调整
- **设备热力图**: 24小时设备利用率分析
- **多维度图表**: 瀑布图、雷达图、桑基图等8种图表

### 🧮 统一算法中心

- **6种优化算法**: 遗传算法、模拟退火、强化学习等
- **智能算法选择**: 根据问题规模自动推荐算法
- **参数自动调优**: 基于历史数据优化算法参数
- **性能监控**: 实时监控算法执行效果

### 🤖 AI智能助手

- **中英文对话**: 支持双语自然语言交互
- **专业模板**: 生产、成本、质量、设备分析模板
- **预测分析**: 需求预测、故障预测、趋势分析
- **智能建议**: 基于数据分析的专业建议

### 💾 统一数据中心

- **多源数据集成**: Excel、邮件、API、MES/ERP系统
- **智能数据清洗**: 自动检测和修复数据质量问题
- **增量数据更新**: 支持数据增量导入和同步
- **数据血缘追踪**: 完整的数据来源和处理记录

## 🔧 配置说明

### 环境变量配置 (.env文件)

```bash
# 数据库配置 (MySQL)
DATABASE_URL=mysql+aiomysql://smart_aps:password@localhost:3306/smart_aps?charset=utf8mb4
DB_HOST=localhost
DB_PORT=3306
DB_USER=smart_aps
DB_PASSWORD=smart_aps_password
DB_NAME=smart_aps

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379

# LLM配置
DEFAULT_LLM_SERVICE=ollama  # ollama 或 azure
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_KEY=your-api-key

# 安全配置
SECRET_KEY=your-super-secret-key-change-in-production
ENCRYPTION_KEY=auto-generated

# 文件上传配置
UPLOAD_DIR=uploads
MAX_UPLOAD_SIZE=209715200  # 200MB

# 认证配置 (可选)
LDAP_ENABLED=false
SSO_ENABLED=false
```

### 主要功能配置示例

#### 1. 前端应用配置

```python
# frontend/config/settings.py
class AppConfig:
    # 基础配置
    APP_NAME = "Smart APS"
    VERSION = "1.0.0"
    DEBUG = os.getenv("DEBUG", "false").lower() == "true"

    # API配置
    API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
    API_TIMEOUT = int(os.getenv("API_TIMEOUT", "30"))

    # 文件上传配置
    MAX_UPLOAD_SIZE = int(os.getenv("MAX_UPLOAD_SIZE", "200")) * 1024 * 1024
    ALLOWED_FILE_TYPES = [
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",  # xlsx
        "application/vnd.ms-excel",  # xls
        "text/csv",  # csv
        "message/rfc822",  # eml
        "application/vnd.ms-outlook"  # msg
    ]

    # LLM配置
    DEFAULT_LLM_SERVICE = os.getenv("DEFAULT_LLM_SERVICE", "ollama")
    OLLAMA_BASE_URL = os.getenv("OLLAMA_BASE_URL", "http://localhost:11434")
```

#### 2. 后端系统配置

```python
# backend/app/core/config.py
class Settings(BaseSettings):
    # 基础配置
    PROJECT_NAME: str = "Smart APS"
    VERSION: str = "1.0.0"
    DEBUG: bool = False

    # 主数据库配置 (MySQL)
    DATABASE_URL: Optional[str] = None
    DB_HOST: str = "localhost"
    DB_PORT: int = 3306
    DB_USER: str = "smart_aps"
    DB_PASSWORD: str = "smart_aps_password"
    DB_NAME: str = "smart_aps"

    # 多数据库配置支持
    # PCI数据库配置
    PCI_DB_URL: Optional[str] = None
    PCI_DB_HOST: str = "localhost"
    PCI_DB_PORT: int = 3306
    PCI_DB_USER: str = "pci_user"
    PCI_DB_PASSWORD: str = "pci_password"
    PCI_DB_NAME: str = "pci_system"
    PCI_DB_ENGINE: str = "mysql"

    # Oracle数据库配置
    ORACLE_DB_URL: Optional[str] = None
    ORACLE_DB_HOST: str = "localhost"
    ORACLE_DB_PORT: int = 1521
    ORACLE_DB_USER: str = "oracle_user"
    ORACLE_DB_PASSWORD: str = "oracle_password"
    ORACLE_DB_NAME: str = "ORCL"
    ORACLE_DB_ENGINE: str = "oracle"

    # 算法配置
    MILP_SOLVER_TIMEOUT: int = 1800  # 30分钟
    MILP_GAP_TOLERANCE: float = 0.01
    MAX_CONCURRENT_CALCULATIONS: int = 2
```

#### 3. 数据库配置方式

Smart APS支持两种数据库配置方式：

##### 方式1: 环境变量配置 (.env文件)

```bash
# 主数据库
DATABASE_URL=mysql+aiomysql://smart_aps:password@localhost:3306/smart_aps?charset=utf8mb4

# PCI数据库
PCI_DB_URL=mysql+aiomysql://pci_user:password@localhost:3306/pci_system?charset=utf8mb4

# Oracle数据库
ORACLE_DB_URL=oracle+oracledb://oracle_user:password@localhost:1521/?service_name=ORCL
```

##### 方式2: 前端界面配置

- 访问 "数据库配置" 页面
- 可视化配置数据库连接
- 支持实时测试和健康检查
- 支持MySQL、Oracle、PostgreSQL、SQL Server、SQLite

### Docker Compose配置

系统提供完整的Docker Compose配置，包括：

- **MySQL 8.0** - 主数据库
- **Redis 7.0** - 缓存和会话管理
- **FastAPI** - 后端API服务
- **Streamlit** - 前端应用
- **Ollama** - 本地LLM服务（可选）

## 🧪 测试

```bash
# 后端测试
cd backend
pytest tests/ -v

# 前端测试
cd frontend
pytest tests/ -v

# 集成测试
docker-compose -f docker-compose.test.yml up --abort-on-container-exit
```

## 📊 性能指标

- **响应时间**: API响应 < 200ms
- **并发用户**: 支持20-30并发用户
- **文件处理**: 支持200MB文件上传
- **算法性能**: MILP求解 < 30分钟

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- **项目主页**: [GitHub](https://github.com/your-org/smart-aps)
- **问题反馈**: [Issues](https://github.com/your-org/smart-aps/issues)
- **技术支持**: <<EMAIL>>

## 🙏 致谢

感谢以下开源项目的支持：

- [FastAPI](https://fastapi.tiangolo.com/)
- [Streamlit](https://streamlit.io/)
- [Plotly](https://plotly.com/)
- [SQLAlchemy](https://www.sqlalchemy.org/)
- [Ollama](https://ollama.ai/)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
