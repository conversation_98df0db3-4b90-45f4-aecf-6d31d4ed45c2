# 📡 Smart APS API 文档

## 📋 API 概览

Smart APS 提供完整的 RESTful API，支持前后端分离架构，所有功能都可以通过 API 访问。

**基础信息**:
- **Base URL**: `http://localhost:8000/api/v1`
- **认证方式**: JWT Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🔐 认证接口

### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123456"
}
```

**响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "token_type": "bearer",
    "expires_in": 3600,
    "user_info": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "管理员"
    }
  }
}
```

### 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

## 📁 文件上传接口

### 上传文件
```http
POST /api/v1/upload/file
Authorization: Bearer <access_token>
Content-Type: multipart/form-data

file: <file_data>
description: "生产数据文件"
auto_process: true
```

**支持的文件类型**:
- Excel文件: `.xlsx`, `.xls`
- CSV文件: `.csv`
- 邮件文件: `.eml`, `.msg`

**响应**:
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "file_id": "uuid-string",
    "filename": "production_data.xlsx",
    "file_size": 1024000,
    "file_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "status": "uploaded",
    "auto_process": true
  }
}
```

### 获取文件处理结果
```http
GET /api/v1/upload/{file_id}/result
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "file_id": "uuid-string",
    "status": "processed",
    "tables": [
      {
        "sheet_name": "生产数据",
        "table_name": "生产数据",
        "columns": ["产品名称", "数量", "交期"],
        "row_count": 100,
        "column_count": 3,
        "data": [
          {
            "产品名称": "产品A",
            "数量": 100,
            "交期": "2024-01-15"
          }
        ]
      }
    ]
  }
}
```

## 🧮 算法执行接口

### 执行生产规划算法
```http
POST /api/v1/algorithms/production-planning
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "algorithm_type": "genetic",
  "data_source": "file_id_or_manual",
  "constraints": {
    "max_capacity": 1000,
    "time_limit": 168,
    "quality_requirement": 0.95
  },
  "parameters": {
    "population_size": 100,
    "generations": 500,
    "mutation_rate": 0.1
  }
}
```

**响应**:
```json
{
  "success": true,
  "message": "算法执行成功",
  "data": {
    "task_id": "task-uuid",
    "algorithm_type": "genetic",
    "status": "completed",
    "execution_time": 120.5,
    "results": {
      "objective_value": 0.95,
      "schedule": [
        {
          "task_id": "T001",
          "product": "产品A",
          "start_time": "2024-01-01 08:00:00",
          "end_time": "2024-01-01 16:00:00",
          "resource": "L01"
        }
      ],
      "metrics": {
        "efficiency": 0.92,
        "utilization": 0.88,
        "quality_score": 0.96
      }
    }
  }
}
```

### 获取算法执行状态
```http
GET /api/v1/algorithms/tasks/{task_id}
Authorization: Bearer <access_token>
```

## 🤖 AI助手接口

### 发送对话消息
```http
POST /api/v1/llm/chat
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "message": "如何提高生产效率？",
  "context": {
    "current_page": "生产规划",
    "data_context": {
      "recent_efficiency": 0.85,
      "target_efficiency": 0.90
    }
  },
  "template_type": "production_analysis"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "response": "基于当前数据分析，建议从以下几个方面提高生产效率：\n1. 优化设备维护计划\n2. 调整生产排程\n3. 提升员工技能培训",
    "suggestions": [
      "查看设备管理页面",
      "运行生产规划优化",
      "查看历史效率趋势"
    ],
    "confidence": 0.92,
    "processing_time": 1.5
  }
}
```

## 📊 数据分析接口

### 获取生产数据统计
```http
GET /api/v1/analytics/production-stats
Authorization: Bearer <access_token>
Query Parameters:
  - start_date: 2024-01-01
  - end_date: 2024-01-31
  - metrics: efficiency,quality,output
```

**响应**:
```json
{
  "success": true,
  "data": {
    "period": {
      "start_date": "2024-01-01",
      "end_date": "2024-01-31"
    },
    "metrics": {
      "efficiency": {
        "average": 0.87,
        "trend": "increasing",
        "data_points": [
          {"date": "2024-01-01", "value": 0.85},
          {"date": "2024-01-02", "value": 0.86}
        ]
      },
      "quality": {
        "average": 0.94,
        "trend": "stable",
        "data_points": []
      }
    }
  }
}
```

## ⚙️ 系统管理接口

### 获取系统状态
```http
GET /api/v1/system/status
Authorization: Bearer <access_token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "system_status": "healthy",
    "version": "1.0.0",
    "uptime": 86400,
    "services": {
      "database": "connected",
      "redis": "connected",
      "llm_service": "available"
    },
    "performance": {
      "cpu_usage": 0.45,
      "memory_usage": 0.62,
      "disk_usage": 0.35
    }
  }
}
```

### 获取用户列表
```http
GET /api/v1/users
Authorization: Bearer <access_token>
Query Parameters:
  - page: 1
  - page_size: 20
  - role: 管理员
```

## 🔧 配置管理接口

### 获取系统配置
```http
GET /api/v1/config/system
Authorization: Bearer <access_token>
```

### 更新系统配置
```http
PUT /api/v1/config/system
Authorization: Bearer <access_token>
Content-Type: application/json

{
  "llm_service": "ollama",
  "max_upload_size": 209715200,
  "default_algorithm": "genetic"
}
```

## 📈 错误处理

### 标准错误响应格式
```json
{
  "success": false,
  "message": "错误描述",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req-uuid"
}
```

### 常见错误码
- `AUTH_001`: 认证失败
- `AUTH_002`: Token过期
- `FILE_001`: 文件格式不支持
- `FILE_002`: 文件大小超限
- `ALG_001`: 算法执行失败
- `DATA_001`: 数据验证失败

## 🚀 使用示例

### Python客户端示例
```python
import requests

class SmartAPSClient:
    def __init__(self, base_url, username, password):
        self.base_url = base_url
        self.token = self._login(username, password)
    
    def _login(self, username, password):
        response = requests.post(
            f"{self.base_url}/auth/login",
            json={"username": username, "password": password}
        )
        return response.json()["data"]["access_token"]
    
    def upload_file(self, file_path):
        headers = {"Authorization": f"Bearer {self.token}"}
        with open(file_path, 'rb') as f:
            files = {"file": f}
            response = requests.post(
                f"{self.base_url}/upload/file",
                headers=headers,
                files=files
            )
        return response.json()
    
    def run_algorithm(self, algorithm_type, constraints):
        headers = {"Authorization": f"Bearer {self.token}"}
        data = {
            "algorithm_type": algorithm_type,
            "constraints": constraints
        }
        response = requests.post(
            f"{self.base_url}/algorithms/production-planning",
            headers=headers,
            json=data
        )
        return response.json()

# 使用示例
client = SmartAPSClient("http://localhost:8000/api/v1", "admin", "password")
result = client.upload_file("production_data.xlsx")
print(result)
```

## 📚 更多信息

- **API测试**: 访问 `http://localhost:8000/docs` 查看交互式API文档
- **认证指南**: 查看 [认证配置文档](./authentication.md)
- **扩展开发**: 查看 [扩展开发指南](./extension_development.md)
