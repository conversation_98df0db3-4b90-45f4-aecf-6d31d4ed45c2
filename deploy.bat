@echo off
REM Smart Planning Docker 一键部署脚本 (Windows版本)
REM 使用方法: deploy.bat [start|stop|restart|logs|status]

setlocal enabledelayedexpansion

REM 项目配置
set PROJECT_NAME=smart-planning
set COMPOSE_FILE=docker-compose.yml
set ENV_FILE=.env

REM 颜色定义 (Windows CMD限制，使用echo代替)
set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

REM 检查Docker和Docker Compose
:check_requirements
echo %INFO% 检查系统要求...

docker --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Docker 未安装，请先安装 Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Docker Compose 未安装，请先安装 Docker Compose
    pause
    exit /b 1
)

echo %SUCCESS% 系统要求检查通过
goto :eof

REM 检查环境配置
:check_environment
echo %INFO% 检查环境配置...

if not exist "%ENV_FILE%" (
    if exist ".env.example" (
        echo %WARNING% .env 文件不存在，从 .env.example 复制...
        copy .env.example .env
        echo %WARNING% 请编辑 .env 文件配置数据库密码等参数
    ) else (
        echo %ERROR% .env 和 .env.example 文件都不存在
        pause
        exit /b 1
    )
)

if not exist "%COMPOSE_FILE%" (
    echo %ERROR% docker-compose.yml 文件不存在
    pause
    exit /b 1
)

echo %SUCCESS% 环境配置检查通过
goto :eof

REM 创建必要的目录
:create_directories
echo %INFO% 创建必要的目录...

if not exist "uploads" mkdir uploads
if not exist "logs" mkdir logs
if not exist "exports" mkdir exports
if not exist "backups" mkdir backups
if not exist "scripts" mkdir scripts
if not exist "scripts\mysql" mkdir scripts\mysql

echo %SUCCESS% 目录创建完成
goto :eof

REM 启动服务
:start_services
echo %INFO% 启动 Smart APS 服务...

REM 拉取最新镜像
echo %INFO% 拉取Docker镜像...
docker-compose pull

REM 构建自定义镜像
echo %INFO% 构建应用镜像...
docker-compose build

REM 启动服务
echo %INFO% 启动所有服务...
docker-compose up -d

REM 等待服务启动
echo %INFO% 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
call :check_services_health

echo %SUCCESS% Smart APS 服务启动完成！
call :show_access_info
goto :eof

REM 停止服务
:stop_services
echo %INFO% 停止 Smart APS 服务...
docker-compose down
echo %SUCCESS% 服务已停止
goto :eof

REM 重启服务
:restart_services
echo %INFO% 重启 Smart APS 服务...
call :stop_services
timeout /t 5 /nobreak >nul
call :start_services
goto :eof

REM 检查服务健康状态
:check_services_health
echo %INFO% 检查服务健康状态...

docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo %WARNING% 部分服务可能存在问题
) else (
    echo %SUCCESS% 服务运行正常
)
goto :eof

REM 显示服务状态
:show_status
echo %INFO% Smart APS 服务状态:
docker-compose ps

echo.
echo %INFO% 容器资源使用情况:
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
goto :eof

REM 显示日志
:show_logs
if "%~2"=="" (
    echo %INFO% 显示所有服务日志...
    docker-compose logs -f
) else (
    echo %INFO% 显示 %~2 服务日志...
    docker-compose logs -f %~2
)
goto :eof

REM 显示访问信息
:show_access_info
echo.
echo %SUCCESS% 🎉 Smart APS 部署完成！
echo.
echo 📱 访问地址:
echo   前端应用: http://localhost:8501
echo   后端API:  http://localhost:8000
echo   API文档:  http://localhost:8000/docs
echo.
echo 🔑 默认账户:
echo   管理员: admin / admin123456
echo   计划员: planner / admin123456
echo   用户:   user / admin123456
echo.
echo 🐳 Docker管理:
echo   查看状态: deploy.bat status
echo   查看日志: deploy.bat logs
echo   停止服务: deploy.bat stop
echo   重启服务: deploy.bat restart
echo.
goto :eof

REM 数据库备份
:backup_database
echo %INFO% 备份数据库...

for /f "tokens=1-4 delims=/ " %%i in ('date /t') do (
    set "backup_date=%%k%%j%%i"
)
for /f "tokens=1-2 delims=: " %%i in ('time /t') do (
    set "backup_time=%%i%%j"
)
set "backup_time=!backup_time: =!"
set "backup_file=backups\smart_aps_backup_!backup_date!_!backup_time!.sql"

docker-compose exec mysql mysqldump -u smart_aps -psmart_aps_password smart_aps > "!backup_file!"

if errorlevel 1 (
    echo %ERROR% 数据库备份失败
    pause
    exit /b 1
) else (
    echo %SUCCESS% 数据库备份完成: !backup_file!
)
goto :eof

REM 清理系统
:cleanup
echo %INFO% 清理Docker资源...

REM 停止服务
docker-compose down

REM 删除未使用的镜像
docker image prune -f

REM 删除未使用的卷
docker volume prune -f

REM 删除未使用的网络
docker network prune -f

echo %SUCCESS% 清理完成
goto :eof

REM 显示帮助信息
:show_help
echo Smart APS Docker 部署脚本 (Windows版本)
echo.
echo 使用方法: %~nx0 [命令]
echo.
echo 命令:
echo   start     启动所有服务 (默认)
echo   stop      停止所有服务
echo   restart   重启所有服务
echo   status    显示服务状态
echo   logs      显示服务日志
echo   backup    备份数据库
echo   cleanup   清理Docker资源
echo   help      显示帮助信息
echo.
goto :eof

REM 主函数
:main
set "command=%~1"
if "%command%"=="" set "command=start"

if "%command%"=="start" (
    call :check_requirements
    call :check_environment
    call :create_directories
    call :start_services
) else if "%command%"=="stop" (
    call :stop_services
) else if "%command%"=="restart" (
    call :restart_services
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="logs" (
    call :show_logs %*
) else if "%command%"=="backup" (
    call :backup_database
) else if "%command%"=="cleanup" (
    call :cleanup
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else (
    echo %ERROR% 未知命令: %command%
    echo 使用 '%~nx0 help' 查看帮助信息
    pause
    exit /b 1
)

pause
goto :eof

REM 执行主函数
call :main %*
