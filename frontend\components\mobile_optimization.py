"""
移动端优化组件
提供移动设备友好的界面和交互
"""

import streamlit as st
from typing import Dict, List, Any, Optional

class MobileOptimization:
    """移动端优化组件"""
    
    @staticmethod
    def apply_mobile_styles():
        """应用移动端样式"""
        st.markdown("""
        <style>
        /* 移动端适配样式 */
        @media (max-width: 768px) {
            .main .block-container {
                padding-left: 1rem;
                padding-right: 1rem;
                padding-top: 1rem;
            }
            
            /* 按钮优化 */
            .stButton > button {
                width: 100%;
                height: 3rem;
                font-size: 16px;
                margin-bottom: 0.5rem;
            }
            
            /* 指标卡片优化 */
            .metric-container {
                background: white;
                padding: 1rem;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                margin-bottom: 1rem;
                text-align: center;
            }
            
            /* 表格优化 */
            .dataframe {
                font-size: 12px;
            }
            
            /* 图表优化 */
            .plotly-graph-div {
                height: 300px !important;
            }
            
            /* 侧边栏优化 */
            .css-1d391kg {
                padding-top: 1rem;
            }
            
            /* 标题优化 */
            h1, h2, h3 {
                font-size: 1.2rem !important;
                margin-bottom: 0.5rem !important;
            }
            
            /* 输入框优化 */
            .stTextInput > div > div > input,
            .stSelectbox > div > div > select {
                height: 3rem;
                font-size: 16px;
            }
        }
        
        /* 触摸友好的按钮 */
        .touch-button {
            background: linear-gradient(135deg, #4A90E2 0%, #50C878 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin: 8px 0;
            min-height: 48px;
        }
        
        .touch-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }
        
        /* 卡片式布局 */
        .mobile-card {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin: 8px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid #4A90E2;
        }
        
        /* 快速操作栏 */
        .quick-actions {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            display: flex;
            gap: 8px;
            z-index: 1000;
        }
        
        .quick-action-btn {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #4A90E2 0%, #50C878 100%);
            color: white;
            font-size: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .quick-action-btn:hover {
            transform: scale(1.1);
        }
        </style>
        """, unsafe_allow_html=True)
    
    @staticmethod
    def create_mobile_header(title: str, subtitle: str = None):
        """创建移动端友好的页面头部"""
        st.markdown(f"""
        <div class="mobile-card">
            <h1 style="margin: 0; color: #2C3E50; font-size: 1.5rem;">{title}</h1>
            {f'<p style="margin: 8px 0 0 0; color: #7F8C8D; font-size: 0.9rem;">{subtitle}</p>' if subtitle else ''}
        </div>
        """, unsafe_allow_html=True)
    
    @staticmethod
    def create_mobile_metrics(metrics: List[Dict[str, Any]]):
        """创建移动端友好的指标展示"""
        for metric in metrics:
            delta_color = "green" if metric.get('delta', 0) >= 0 else "red"
            delta_symbol = "↗" if metric.get('delta', 0) >= 0 else "↘"
            
            st.markdown(f"""
            <div class="mobile-card">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div>
                        <h3 style="margin: 0; color: #2C3E50; font-size: 1rem;">{metric['title']}</h3>
                        <h2 style="margin: 4px 0 0 0; color: #4A90E2; font-size: 1.8rem;">{metric['value']}</h2>
                    </div>
                    <div style="text-align: right;">
                        <span style="font-size: 2rem;">{metric.get('icon', '📊')}</span>
                        {f'<div style="color: {delta_color}; font-size: 0.9rem; margin-top: 4px;">{delta_symbol} {metric["delta"]}</div>' if 'delta' in metric else ''}
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)
    
    @staticmethod
    def create_mobile_action_buttons(actions: List[Dict[str, Any]]):
        """创建移动端友好的操作按钮"""
        for action in actions:
            if st.button(
                f"{action.get('icon', '🔧')} {action['label']}", 
                key=f"mobile_action_{action['key']}",
                help=action.get('description', '')
            ):
                if 'callback' in action:
                    action['callback']()
                return action['key']
        return None
    
    @staticmethod
    def create_mobile_tabs(tabs_config: List[Dict[str, Any]]):
        """创建移动端友好的标签页"""
        tab_names = [tab['name'] for tab in tabs_config]
        tabs = st.tabs(tab_names)
        
        for i, (tab, config) in enumerate(zip(tabs, tabs_config)):
            with tab:
                if 'content_func' in config:
                    config['content_func']()
                elif 'content' in config:
                    st.markdown(config['content'])
    
    @staticmethod
    def create_mobile_data_table(data: List[Dict[str, Any]], max_rows: int = 10):
        """创建移动端友好的数据表格"""
        if not data:
            st.info("暂无数据")
            return
        
        # 限制显示行数
        display_data = data[:max_rows]
        
        for i, row in enumerate(display_data):
            with st.expander(f"📋 记录 {i+1}", expanded=False):
                for key, value in row.items():
                    st.markdown(f"**{key}**: {value}")
        
        if len(data) > max_rows:
            st.info(f"显示前 {max_rows} 条记录，共 {len(data)} 条")
    
    @staticmethod
    def create_mobile_chart_container(chart_func, title: str = None):
        """创建移动端友好的图表容器"""
        if title:
            st.markdown(f"""
            <div class="mobile-card">
                <h3 style="margin: 0 0 16px 0; color: #2C3E50;">{title}</h3>
            </div>
            """, unsafe_allow_html=True)
        
        # 设置移动端图表高度
        with st.container():
            chart_func()
    
    @staticmethod
    def create_quick_actions_bar(actions: List[Dict[str, Any]]):
        """创建底部快速操作栏"""
        actions_html = ""
        for action in actions:
            actions_html += f"""
            <button class="quick-action-btn" 
                    onclick="window.parent.postMessage({{type: 'quick_action', action: '{action['key']}'}}, '*')"
                    title="{action.get('title', action['label'])}">
                {action.get('icon', '🔧')}
            </button>
            """
        
        st.markdown(f"""
        <div class="quick-actions">
            {actions_html}
        </div>
        """, unsafe_allow_html=True)
    
    @staticmethod
    def detect_mobile_device():
        """检测是否为移动设备"""
        # 通过JavaScript检测设备类型
        st.markdown("""
        <script>
        function detectMobile() {
            const isMobile = window.innerWidth <= 768 || 
                           /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (isMobile) {
                document.body.classList.add('mobile-device');
                // 通知Streamlit应用
                window.parent.postMessage({type: 'device_type', isMobile: true}, '*');
            }
        }
        
        detectMobile();
        window.addEventListener('resize', detectMobile);
        </script>
        """, unsafe_allow_html=True)
    
    @staticmethod
    def create_mobile_navigation_drawer():
        """创建移动端导航抽屉"""
        with st.sidebar:
            st.markdown("### 📱 快速导航")
            
            nav_items = [
                ("📋", "综合仪表板", "01_综合仪表板"),
                ("📁", "数据上传", "02_数据上传"),
                ("🏭", "生产规划", "03_生产规划"),
                ("⚙️", "设备管理", "04_设备管理"),
                ("📊", "计划监控", "05_计划监控"),
                ("📈", "数据分析", "06_数据分析"),
                ("🤖", "智能助手", "07_智能助手")
            ]
            
            for icon, name, page in nav_items:
                if st.button(f"{icon} {name}", key=f"nav_{page}", use_container_width=True):
                    st.switch_page(f"pages/{page}.py")
    
    @staticmethod
    def create_mobile_search_bar():
        """创建移动端搜索栏"""
        st.markdown("""
        <div style="
            background: white;
            border-radius: 25px;
            padding: 8px 16px;
            margin: 16px 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
        ">
            <span style="margin-right: 8px; color: #7F8C8D;">🔍</span>
            <input type="text" 
                   placeholder="搜索功能、数据或设备..." 
                   style="
                       border: none;
                       outline: none;
                       flex: 1;
                       font-size: 16px;
                       background: transparent;
                   ">
        </div>
        """, unsafe_allow_html=True)
    
    @staticmethod
    def optimize_for_mobile():
        """一键应用所有移动端优化"""
        MobileOptimization.apply_mobile_styles()
        MobileOptimization.detect_mobile_device()
        
        # 在session state中标记已应用移动端优化
        if 'mobile_optimized' not in st.session_state:
            st.session_state.mobile_optimized = True
