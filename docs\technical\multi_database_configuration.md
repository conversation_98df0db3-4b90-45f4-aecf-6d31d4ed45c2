# 🗄️ Smart APS 多数据库配置指南

## 📋 概述

Smart APS 支持多数据库配置模式，允许连接不同的数据库系统来查询库存信息、生产数据分析等。系统采用配置化的方式，通过环境变量灵活配置多个数据库连接。

## 🏗️ 支持的数据库类型

| 数据库类型 | 用途 | 配置前缀 |
|-----------|------|----------|
| **main** | 主数据库 - Smart APS核心数据 | `DB_` |
| **inventory** | 库存数据库 - 库存管理数据 | `INVENTORY_DB_` |
| **production_analysis** | 生产数据分析数据库 | `PRODUCTION_ANALYSIS_DB_` |
| **erp** | ERP系统数据库 - 企业资源规划 | `ERP_DB_` |
| **mes** | MES系统数据库 - 制造执行系统 | `MES_DB_` |

## ⚙️ 配置方法

### 1. 环境变量配置

在 `.env` 文件中配置多个数据库连接：

```bash
# =============================================================================
# 主数据库配置 (必需)
# =============================================================================
DATABASE_URL=mysql+aiomysql://smart_aps:password@localhost:3306/smart_aps?charset=utf8mb4
DB_HOST=localhost
DB_PORT=3306
DB_USER=smart_aps
DB_PASSWORD=smart_aps_password
DB_NAME=smart_aps

# =============================================================================
# 库存数据库配置 (可选)
# =============================================================================
INVENTORY_DB_URL=mysql+aiomysql://inventory_user:inventory_password@localhost:3306/inventory_db?charset=utf8mb4
INVENTORY_DB_HOST=localhost
INVENTORY_DB_PORT=3306
INVENTORY_DB_USER=inventory_user
INVENTORY_DB_PASSWORD=inventory_password
INVENTORY_DB_NAME=inventory_db

# =============================================================================
# 生产数据分析数据库配置 (可选)
# =============================================================================
PRODUCTION_ANALYSIS_DB_URL=mysql+aiomysql://production_user:production_password@localhost:3306/data_analysis?charset=utf8mb4
PRODUCTION_ANALYSIS_DB_HOST=localhost
PRODUCTION_ANALYSIS_DB_PORT=3306
PRODUCTION_ANALYSIS_DB_USER=production_user
PRODUCTION_ANALYSIS_DB_PASSWORD=production_password
PRODUCTION_ANALYSIS_DB_NAME=data_analysis

# =============================================================================
# ERP系统数据库配置 (可选)
# =============================================================================
ERP_DB_URL=mysql+aiomysql://erp_user:erp_password@localhost:3306/erp_system?charset=utf8mb4
ERP_DB_HOST=localhost
ERP_DB_PORT=3306
ERP_DB_USER=erp_user
ERP_DB_PASSWORD=erp_password
ERP_DB_NAME=erp_system

# =============================================================================
# MES系统数据库配置 (可选)
# =============================================================================
MES_DB_URL=mysql+aiomysql://mes_user:mes_password@localhost:3306/mes_system?charset=utf8mb4
MES_DB_HOST=localhost
MES_DB_PORT=3306
MES_DB_USER=mes_user
MES_DB_PASSWORD=mes_password
MES_DB_NAME=mes_system
```

### 2. 配置说明

- **完整URL优先**: 如果设置了 `*_DB_URL`，将优先使用完整URL
- **分项配置**: 如果没有设置完整URL，系统会根据 `HOST`、`PORT`、`USER`、`PASSWORD`、`NAME` 自动构建连接字符串
- **可选配置**: 除主数据库外，其他数据库都是可选的，未配置的数据库不会初始化连接

## 🔧 使用方法

### 1. Python代码中使用

#### 基础查询服务

```python
from app.services.database_query_service import database_query_service

# 查询库存数据
inventory_data = await database_query_service.query_inventory_data(
    "SELECT * FROM inventory WHERE status = 'available'"
)

# 查询生产分析数据
production_data = await database_query_service.query_production_analysis_data(
    "SELECT * FROM production_records WHERE date >= :start_date",
    {"start_date": "2024-01-01"}
)

# 查询ERP数据
erp_data = await database_query_service.query_erp_data(
    "SELECT * FROM sales_orders WHERE status = :status",
    {"status": "pending"}
)
```

#### 专用查询服务

```python
from app.services.database_query_service import (
    inventory_query_service,
    production_analysis_query_service
)

# 获取低库存商品
low_stock = await inventory_query_service.get_low_stock_items(threshold=100)

# 获取即将过期商品
expiring = await inventory_query_service.get_expiring_items(days=30)

# 获取效率趋势
efficiency = await production_analysis_query_service.get_efficiency_trends(
    production_line="L01", 
    days=30
)
```

### 2. API接口使用

#### 获取数据库状态

```http
GET /api/v1/database/status
Authorization: Bearer <token>
```

**响应**:
```json
{
  "success": true,
  "data": {
    "main": {
      "configured": true,
      "healthy": true,
      "connection_info": {...}
    },
    "inventory": {
      "configured": true,
      "healthy": true,
      "connection_info": {...}
    },
    "production_analysis": {
      "configured": false,
      "healthy": null
    }
  }
}
```

#### 查询库存数据

```http
GET /api/v1/database/inventory/summary
Authorization: Bearer <token>
```

#### 查询生产效率数据

```http
GET /api/v1/database/production/efficiency?start_date=2024-01-01&end_date=2024-01-31
Authorization: Bearer <token>
```

#### 执行自定义查询

```http
POST /api/v1/database/query/custom
Authorization: Bearer <token>
Content-Type: application/json

{
  "database_type": "inventory",
  "query": "SELECT item_code, SUM(quantity) as total FROM inventory WHERE location = :location GROUP BY item_code",
  "parameters": {
    "location": "仓库1"
  }
}
```

### 3. 前端使用示例

```python
import streamlit as st
import requests

# 获取库存汇总
@st.cache_data
def get_inventory_summary():
    response = requests.get(
        f"{API_BASE_URL}/database/inventory/summary",
        headers={"Authorization": f"Bearer {token}"}
    )
    return response.json()

# 显示库存数据
inventory_data = get_inventory_summary()
if inventory_data["success"]:
    st.dataframe(inventory_data["data"])
```

## 📊 实际使用场景

### 场景1: 查询库存信息

```python
# 连接库存数据库查询当前库存
inventory_summary = await database_query_service.query_inventory_data("""
    SELECT 
        item_code,
        item_name,
        SUM(quantity) as total_quantity,
        location,
        status
    FROM inventory 
    WHERE status = 'available'
    GROUP BY item_code, item_name, location, status
    ORDER BY total_quantity DESC
""")
```

### 场景2: 查询生产数据分析

```python
# 连接生产数据分析数据库查询效率数据
efficiency_data = await database_query_service.query_production_analysis_data("""
    SELECT 
        production_line,
        DATE(production_date) as date,
        AVG(efficiency) as avg_efficiency,
        SUM(output_quantity) as total_output
    FROM production_records 
    WHERE production_date BETWEEN :start_date AND :end_date
    GROUP BY production_line, DATE(production_date)
    ORDER BY date DESC
""", {
    "start_date": "2024-01-01",
    "end_date": "2024-01-31"
})
```

### 场景3: 跨数据库数据整合

```python
# 同时查询多个数据库并整合数据
async def get_comprehensive_report():
    # 获取库存数据
    inventory = await database_query_service.query_inventory_data(
        "SELECT item_code, SUM(quantity) as stock FROM inventory GROUP BY item_code"
    )
    
    # 获取生产计划
    production = await database_query_service.query_erp_data(
        "SELECT product_code, SUM(quantity) as planned FROM production_plans GROUP BY product_code"
    )
    
    # 整合数据
    report = merge_inventory_and_production(inventory, production)
    return report
```

## 🔍 监控和维护

### 1. 健康检查

```python
# 检查所有数据库健康状态
status = await database_query_service.get_database_status()
for db_name, db_status in status.items():
    if db_status["configured"] and not db_status["healthy"]:
        logger.warning(f"数据库 {db_name} 连接异常")
```

### 2. 连接池监控

```python
# 获取连接池信息
from app.core.database import multi_db_manager

conn_info = await multi_db_manager.get_all_connection_info()
for db_type, info in conn_info.items():
    print(f"{db_type}: {info['checked_out']}/{info['pool_size']} 连接使用中")
```

## ⚠️ 注意事项

### 1. 安全考虑

- **权限控制**: 为每个数据库创建专用用户，只授予必要权限
- **密码安全**: 使用强密码，定期更换
- **网络安全**: 配置防火墙，限制数据库访问

### 2. 性能优化

- **连接池**: 合理配置连接池大小
- **查询优化**: 使用索引，避免全表扫描
- **缓存策略**: 对频繁查询的数据进行缓存

### 3. 错误处理

- **连接失败**: 系统会自动重试连接
- **查询超时**: 设置合理的查询超时时间
- **数据一致性**: 注意跨数据库事务的一致性问题

## 🚀 最佳实践

1. **配置管理**: 使用环境变量管理数据库配置，不要硬编码
2. **连接复用**: 使用连接池，避免频繁创建连接
3. **错误监控**: 实施完善的错误监控和告警机制
4. **性能监控**: 定期监控查询性能，优化慢查询
5. **备份策略**: 制定完善的数据备份和恢复策略

---

**🎯 通过多数据库配置，Smart APS 可以灵活连接各种外部系统，实现数据的统一管理和分析！**
