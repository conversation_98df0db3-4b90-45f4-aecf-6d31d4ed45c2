# 智能工厂生产管理规划系统 - 技术评估与最佳实践

## 技术选型评估

### 1. 后端技术栈评估

#### Python vs Java vs .NET

| 评估维度 | Python | Java | .NET | 选择理由 |
|---------|--------|------|------|---------|
| 开发效率 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ | Python语法简洁，开发速度快 |
| 算法生态 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | 丰富的科学计算和ML库 |
| 性能表现 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 对于计算密集型任务可用C扩展 |
| 团队技能 | ⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ | 团队Python经验丰富 |
| 社区支持 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ | 活跃的开源社区 |

**最终选择：Python**
- 丰富的数据科学和优化算法库（NumPy、SciPy、PuLP）
- 快速原型开发和迭代
- 优秀的LLM集成支持
- 团队技能匹配

#### Web框架选择：FastAPI vs Django vs Flask

| 特性 | FastAPI | Django | Flask | 评分 |
|------|---------|--------|-------|------|
| 性能 | 高 | 中 | 中 | FastAPI胜出 |
| 异步支持 | 原生 | 部分 | 需扩展 | FastAPI胜出 |
| API文档 | 自动生成 | 需手动 | 需手动 | FastAPI胜出 |
| 学习曲线 | 平缓 | 陡峭 | 平缓 | FastAPI/Flask并列 |
| 生态系统 | 新兴 | 成熟 | 成熟 | Django胜出 |

**选择FastAPI的原因**：
- 原生异步支持，适合高并发场景
- 自动API文档生成，减少维护成本
- 类型提示支持，提高代码质量
- 现代化设计理念

### 2. 数据库技术评估

#### 关系型数据库选择

| 数据库 | 优势 | 劣势 | 适用场景 |
|--------|------|------|---------|
| MySQL 8.0 | 成熟稳定、性能优秀、社区活跃 | 复杂查询性能一般 | 主要业务数据 ✅ |
| PostgreSQL | 功能丰富、扩展性强、JSON支持 | 配置复杂、内存占用高 | 复杂分析查询 |
| SQL Server | 企业级功能、BI集成好 | 成本高、Linux支持有限 | 企业环境集成 |

**选择MySQL的原因**：
- 团队熟悉度高
- 性能稳定可靠
- 运维成本低
- 云服务支持好

#### 缓存技术选择

| 技术 | 优势 | 劣势 | 使用场景 |
|------|------|------|---------|
| Redis | 数据结构丰富、持久化支持 | 内存占用高 | 会话、缓存、队列 ✅ |
| Memcached | 简单高效、内存利用率高 | 功能单一 | 简单缓存 |
| 本地缓存 | 访问速度快、无网络开销 | 数据一致性问题 | 静态数据缓存 |

### 3. 前端技术评估

#### 框架选择：Streamlit vs Dash vs React

| 框架 | 学习成本 | 开发效率 | 数据可视化 | 交互性 | 小团队适用性 | 选择 |
|------|---------|---------|------------|--------|-------------|------|
| Streamlit | 极低 | 极高 | 优秀 | 良好 | 优秀 | ✅ 主要 |
| Dash | 低 | 高 | 优秀 | 优秀 | 良好 | 备选 |
| React | 高 | 中 | 需集成 | 优秀 | 一般 | 复杂场景 |

**选择Streamlit的原因**：
- **快速开发**：Python原生，无需前后端分离
- **丰富图表**：内置Plotly、Altair等多种图表库
- **交互性强**：支持实时数据更新和用户交互
- **学习成本低**：Python开发者可快速上手
- **适合小团队**：减少技术栈复杂度，降低维护成本

#### 图表库选择对比

| 图表库 | 交互性 | 图表类型 | 学习成本 | 性能 | 使用场景 |
|--------|--------|---------|---------|------|---------|
| Plotly | 优秀 | 丰富 | 中 | 良好 | 复杂交互图表 ✅ |
| Altair | 良好 | 丰富 | 低 | 优秀 | 统计图表 ✅ |
| Matplotlib | 一般 | 丰富 | 高 | 优秀 | 静态图表 |
| Bokeh | 优秀 | 中等 | 高 | 良好 | Web交互图表 |

## 架构设计最佳实践

### 1. 小规模单体架构原则

#### 模块化单体设计
```python
# 适合小团队的模块化单体架构
app_modules = {
    "auth": {
        "responsibility": "用户管理、认证授权",
        "components": ["login", "permissions", "session_management"]
    },
    "data_integration": {
        "responsibility": "数据源集成、文件处理",
        "components": ["file_upload", "email_parser", "excel_processor", "data_validation"]
    },
    "planning": {
        "responsibility": "生产规划、算法执行",
        "components": ["demand_calculation", "capacity_planning", "milp_solver", "constraint_manager"]
    },
    "llm_integration": {
        "responsibility": "LLM服务集成",
        "components": ["ollama_client", "azure_openai_client", "prompt_manager"]
    },
    "visualization": {
        "responsibility": "数据可视化、用户界面",
        "components": ["streamlit_app", "chart_components", "dashboard"]
    }
}

# 单体架构的优势（适合小团队）
advantages = {
    "简单部署": "单一应用，部署简单",
    "开发效率": "无需处理分布式复杂性",
    "调试容易": "所有代码在一个进程中",
    "性能优秀": "无网络调用开销",
    "事务简单": "本地事务，无分布式事务复杂性"
}
```

#### 服务间通信模式
```python
# 1. 同步通信：REST API
class ServiceClient:
    async def call_service(self, service_name: str, endpoint: str, data: dict):
        url = f"http://{service_name}-service:8000{endpoint}"
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=data)
            return response.json()

# 2. 异步通信：消息队列
class EventPublisher:
    def __init__(self, redis_client):
        self.redis = redis_client

    async def publish_event(self, event_type: str, data: dict):
        event = {
            "type": event_type,
            "data": data,
            "timestamp": datetime.utcnow().isoformat(),
            "id": str(uuid.uuid4())
        }
        await self.redis.publish(f"events:{event_type}", json.dumps(event))

# 3. 服务发现
class ServiceRegistry:
    def __init__(self):
        self.services = {}

    def register_service(self, name: str, host: str, port: int):
        self.services[name] = {"host": host, "port": port, "healthy": True}

    def discover_service(self, name: str) -> dict:
        return self.services.get(name)
```

### 2. 数据一致性策略（单体应用简化版）

#### 本地事务处理
```python
from sqlalchemy.orm import Session
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)

@contextmanager
def database_transaction(session: Session):
    """数据库事务上下文管理器"""
    try:
        yield session
        session.commit()
        logger.info("事务提交成功")
    except Exception as e:
        session.rollback()
        logger.error(f"事务回滚: {str(e)}")
        raise
    finally:
        session.close()

# 使用示例：创建生产计划
async def create_production_plan(plan_data: dict, session: Session):
    """创建生产计划（单体应用事务处理）"""
    with database_transaction(session) as db:
        try:
            # 步骤1：创建计划记录
            plan = ProductionPlan(**plan_data)
            db.add(plan)
            db.flush()  # 获取plan.id

            # 步骤2：创建计划明细
            for detail_data in plan_data.get("details", []):
                detail = ProductionPlanDetail(
                    plan_id=plan.id,
                    **detail_data
                )
                db.add(detail)

            # 步骤3：更新设备状态
            for equipment_id in plan_data.get("equipment_ids", []):
                equipment = db.query(Equipment).filter_by(id=equipment_id).first()
                if equipment:
                    equipment.status = "scheduled"
                    equipment.updated_at = datetime.utcnow()

            # 步骤4：记录操作日志
            log_entry = OperationLog(
                operation_type="create_plan",
                operation_data=plan_data,
                user_id=plan_data.get("created_by"),
                timestamp=datetime.utcnow()
            )
            db.add(log_entry)

            logger.info(f"生产计划创建成功: {plan.id}")
            return {"status": "success", "plan_id": plan.id}

        except Exception as e:
            logger.error(f"生产计划创建失败: {str(e)}")
            raise

# 乐观锁处理并发更新
class OptimisticLockMixin:
    """乐观锁混入类"""

    def update_with_version_check(self, session: Session, **kwargs):
        """带版本检查的更新"""
        current_version = self.version

        # 更新数据
        for key, value in kwargs.items():
            setattr(self, key, value)

        # 增加版本号
        self.version = current_version + 1
        self.updated_at = datetime.utcnow()

        # 执行更新（包含版本检查）
        result = session.query(self.__class__).filter(
            self.__class__.id == self.id,
            self.__class__.version == current_version
        ).update({
            **kwargs,
            'version': self.version,
            'updated_at': self.updated_at
        })

        if result == 0:
            raise ConcurrentUpdateError("数据已被其他用户修改，请刷新后重试")

        session.commit()
```

### 3. 缓存策略最佳实践

#### 多级缓存架构
```python
class MultiLevelCache:
    def __init__(self, l1_cache, l2_cache, l3_cache):
        self.l1 = l1_cache  # 本地内存缓存
        self.l2 = l2_cache  # Redis缓存
        self.l3 = l3_cache  # 数据库

    async def get(self, key: str):
        # L1缓存查找
        value = self.l1.get(key)
        if value is not None:
            return value

        # L2缓存查找
        value = await self.l2.get(key)
        if value is not None:
            self.l1.set(key, value, ttl=300)  # 5分钟
            return value

        # L3数据库查找
        value = await self.l3.get(key)
        if value is not None:
            await self.l2.set(key, value, ttl=3600)  # 1小时
            self.l1.set(key, value, ttl=300)  # 5分钟
            return value

        return None

    async def set(self, key: str, value, ttl: int = None):
        # 写入所有层级
        await self.l3.set(key, value)
        await self.l2.set(key, value, ttl=ttl or 3600)
        self.l1.set(key, value, ttl=min(ttl or 300, 300))

    async def invalidate(self, key: str):
        # 失效所有层级
        self.l1.delete(key)
        await self.l2.delete(key)
        # 注意：通常不删除数据库数据，只是标记为过期
```

#### 缓存更新策略
```python
# 1. Cache-Aside模式
class CacheAsidePattern:
    async def get_equipment_info(self, equipment_id: str):
        # 先查缓存
        cache_key = f"equipment:{equipment_id}"
        cached_data = await self.cache.get(cache_key)

        if cached_data:
            return cached_data

        # 缓存未命中，查数据库
        equipment = await self.db.get_equipment(equipment_id)
        if equipment:
            # 写入缓存
            await self.cache.set(cache_key, equipment, ttl=1800)

        return equipment

    async def update_equipment_info(self, equipment_id: str, data: dict):
        # 更新数据库
        await self.db.update_equipment(equipment_id, data)

        # 删除缓存，下次访问时重新加载
        cache_key = f"equipment:{equipment_id}"
        await self.cache.delete(cache_key)

# 2. Write-Through模式
class WriteThroughPattern:
    async def update_equipment_info(self, equipment_id: str, data: dict):
        # 同时更新数据库和缓存
        await self.db.update_equipment(equipment_id, data)

        cache_key = f"equipment:{equipment_id}"
        await self.cache.set(cache_key, data, ttl=1800)
```

## 性能优化最佳实践

### 1. 数据库优化

#### 查询优化策略
```sql
-- 1. 使用合适的索引
-- 复合索引优化多条件查询
CREATE INDEX idx_plan_status_date ON production_plans(status, created_at);

-- 覆盖索引避免回表查询
CREATE INDEX idx_equipment_cover ON equipment(equipment_type, status)
INCLUDE (equipment_name, capacity);

-- 2. 分区表优化大数据量
CREATE TABLE production_logs (
    id BIGINT AUTO_INCREMENT,
    equipment_id VARCHAR(50),
    log_time DATETIME,
    log_data JSON,
    PRIMARY KEY (id, log_time)
) PARTITION BY RANGE (YEAR(log_time)) (
    PARTITION p2023 VALUES LESS THAN (2024),
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);

-- 3. 查询重写优化
-- 避免SELECT *
SELECT equipment_id, equipment_name, status
FROM equipment
WHERE status = 1;

-- 使用EXISTS代替IN（大数据集）
SELECT p.plan_id
FROM production_plans p
WHERE EXISTS (
    SELECT 1 FROM production_plan_details pd
    WHERE pd.plan_id = p.id AND pd.status = 'executing'
);

-- 使用LIMIT避免全表扫描
SELECT * FROM production_logs
WHERE equipment_id = 'EQ001'
ORDER BY log_time DESC
LIMIT 100;
```

#### 连接池优化
```python
# 数据库连接池配置
DATABASE_CONFIG = {
    "pool_size": 20,          # 连接池大小
    "max_overflow": 30,       # 最大溢出连接数
    "pool_timeout": 30,       # 获取连接超时时间
    "pool_recycle": 3600,     # 连接回收时间
    "pool_pre_ping": True,    # 连接前ping测试
}

# 连接池监控
class DatabaseMonitor:
    def __init__(self, engine):
        self.engine = engine

    def get_pool_status(self):
        pool = self.engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }

    async def monitor_slow_queries(self):
        """监控慢查询"""
        slow_queries = await self.engine.execute("""
            SELECT query_time, sql_text, rows_examined
            FROM mysql.slow_log
            WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ORDER BY query_time DESC
            LIMIT 10
        """)

        for query in slow_queries:
            if query.query_time > 5:  # 超过5秒的查询
                logger.warning(f"慢查询检测: {query.sql_text[:100]}...")
```

### 2. 算法性能优化

#### 并行计算优化
```python
import asyncio
import concurrent.futures
from multiprocessing import Pool, cpu_count

class ParallelOptimizer:
    def __init__(self):
        self.cpu_cores = cpu_count()
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.cpu_cores
        )
        self.process_pool = concurrent.futures.ProcessPoolExecutor(
            max_workers=self.cpu_cores
        )

    async def parallel_equipment_calculation(self, equipment_list):
        """并行计算设备指标"""
        # CPU密集型任务使用进程池
        loop = asyncio.get_event_loop()

        tasks = []
        for equipment in equipment_list:
            task = loop.run_in_executor(
                self.process_pool,
                self._calculate_equipment_metrics,
                equipment
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks)
        return results

    def _calculate_equipment_metrics(self, equipment):
        """计算单个设备指标（CPU密集型）"""
        # 复杂的数学计算
        import numpy as np

        # 模拟复杂计算
        data = np.random.random((1000, 1000))
        result = np.linalg.inv(data @ data.T)

        return {
            "equipment_id": equipment["id"],
            "oee": np.mean(result),
            "utilization": np.std(result)
        }

    async def parallel_io_operations(self, data_sources):
        """并行IO操作"""
        # IO密集型任务使用线程池
        loop = asyncio.get_event_loop()

        tasks = []
        for source in data_sources:
            task = loop.run_in_executor(
                self.thread_pool,
                self._fetch_data_from_source,
                source
            )
            tasks.append(task)

        results = await asyncio.gather(*tasks)
        return results

    def _fetch_data_from_source(self, source):
        """从数据源获取数据（IO密集型）"""
        import requests
        import time

        # 模拟网络请求
        time.sleep(0.1)  # 模拟网络延迟

        return {
            "source": source["name"],
            "data": f"data_from_{source['name']}",
            "timestamp": time.time()
        }
```

#### 算法优化技巧
```python
# 1. 使用NumPy向量化操作
import numpy as np

class OptimizedCalculations:
    def calculate_batch_oee(self, availability_data, performance_data, quality_data):
        """批量计算OEE（向量化）"""
        # 避免循环，使用NumPy向量化操作
        availability = np.array(availability_data)
        performance = np.array(performance_data)
        quality = np.array(quality_data)

        # 向量化计算
        oee = availability * performance * quality

        return oee.tolist()

    def optimize_with_caching(self, problem_data):
        """使用缓存优化重复计算"""
        cache_key = self._generate_cache_key(problem_data)

        # 检查缓存
        cached_result = self.cache.get(cache_key)
        if cached_result:
            return cached_result

        # 执行计算
        result = self._expensive_calculation(problem_data)

        # 缓存结果
        self.cache.set(cache_key, result, ttl=3600)

        return result

    def _generate_cache_key(self, data):
        """生成缓存键"""
        import hashlib

        # 使用数据的哈希值作为缓存键
        data_str = json.dumps(data, sort_keys=True)
        return hashlib.md5(data_str.encode()).hexdigest()

# 2. 内存优化
class MemoryOptimizedProcessor:
    def process_large_dataset(self, data_iterator):
        """流式处理大数据集"""
        results = []

        # 分批处理，避免内存溢出
        batch_size = 1000
        batch = []

        for item in data_iterator:
            batch.append(item)

            if len(batch) >= batch_size:
                # 处理批次
                batch_result = self._process_batch(batch)
                results.extend(batch_result)

                # 清空批次，释放内存
                batch.clear()

        # 处理最后一批
        if batch:
            batch_result = self._process_batch(batch)
            results.extend(batch_result)

        return results

    def _process_batch(self, batch):
        """处理单个批次"""
        # 实现批次处理逻辑
        return [self._process_item(item) for item in batch]
```

### 3. Streamlit前端性能优化

#### Streamlit应用优化策略
```python
import streamlit as st
import pandas as pd
import plotly.express as px
from functools import lru_cache
import time

# 1. 使用缓存优化数据加载
@st.cache_data(ttl=300)  # 缓存5分钟
def load_production_data():
    """缓存生产数据加载"""
    # 模拟数据库查询
    time.sleep(2)  # 模拟耗时操作
    return pd.read_sql("SELECT * FROM production_plans", connection)

@st.cache_data
def process_chart_data(data: pd.DataFrame) -> pd.DataFrame:
    """缓存图表数据处理"""
    # 复杂的数据处理逻辑
    processed = data.groupby(['equipment', 'date']).agg({
        'quantity': 'sum',
        'utilization': 'mean'
    }).reset_index()
    return processed

# 2. 分页处理大数据集
def paginate_dataframe(df: pd.DataFrame, page_size: int = 50):
    """分页显示大数据集"""
    total_pages = len(df) // page_size + (1 if len(df) % page_size > 0 else 0)

    if total_pages > 1:
        page = st.selectbox("选择页面", range(1, total_pages + 1))
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        return df.iloc[start_idx:end_idx]
    return df

# 3. 异步数据加载
@st.fragment
def load_equipment_status():
    """异步加载设备状态"""
    with st.spinner("加载设备状态..."):
        status_data = fetch_equipment_status()
        st.metric("在线设备", status_data['online_count'])
        st.metric("设备利用率", f"{status_data['avg_utilization']:.1%}")

# 4. 条件渲染优化
def render_production_dashboard():
    """条件渲染生产仪表盘"""
    # 只在需要时渲染复杂组件
    if st.session_state.get('show_detailed_charts', False):
        render_detailed_charts()
    else:
        st.button("显示详细图表", key="show_charts",
                 on_click=lambda: st.session_state.update({'show_detailed_charts': True}))

# 5. 图表性能优化
def create_optimized_gantt_chart(data: pd.DataFrame):
    """优化的甘特图"""
    # 限制显示数据量
    if len(data) > 1000:
        st.warning("数据量较大，显示最近1000条记录")
        data = data.tail(1000)

    # 使用高效的图表配置
    fig = px.timeline(
        data,
        x_start="start_time",
        x_end="end_time",
        y="equipment",
        color="product",
        hover_data=["order_id", "quantity"]
    )

    # 优化图表配置
    fig.update_layout(
        height=400,  # 固定高度
        showlegend=False,  # 隐藏图例以提升性能
        hovermode='closest'
    )

    return fig

# 6. 会话状态管理
class SessionStateManager:
    """会话状态管理器"""

    @staticmethod
    def init_session_state():
        """初始化会话状态"""
        if 'data_loaded' not in st.session_state:
            st.session_state.data_loaded = False
        if 'current_plan' not in st.session_state:
            st.session_state.current_plan = None
        if 'filters' not in st.session_state:
            st.session_state.filters = {}

    @staticmethod
    def clear_cache():
        """清理缓存"""
        st.cache_data.clear()
        st.session_state.clear()
        st.rerun()

# 7. 内存优化
def optimize_dataframe_memory(df: pd.DataFrame) -> pd.DataFrame:
    """优化DataFrame内存使用"""
    # 转换数据类型以节省内存
    for col in df.select_dtypes(include=['int64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='integer')

    for col in df.select_dtypes(include=['float64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='float')

    for col in df.select_dtypes(include=['object']).columns:
        if df[col].nunique() / len(df) < 0.5:  # 如果唯一值比例小于50%
            df[col] = df[col].astype('category')

    return df

# 8. 实时数据更新优化
def setup_auto_refresh():
    """设置自动刷新"""
    refresh_interval = st.selectbox(
        "刷新间隔",
        [30, 60, 300, 600],
        format_func=lambda x: f"{x}秒"
    )

    if st.checkbox("启用自动刷新"):
        # 使用st.empty()容器进行局部更新
        placeholder = st.empty()

        while True:
            with placeholder.container():
                display_real_time_data()
            time.sleep(refresh_interval)
```

#### Streamlit部署优化
```python
# streamlit_config.toml
[server]
port = 8501
enableCORS = false
enableXsrfProtection = false
maxUploadSize = 200  # MB

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"

# 启动优化
# streamlit run app.py --server.maxUploadSize=200 --server.enableCORS=false
```

## 安全最佳实践

### 1. API安全

#### 输入验证和清理
```python
from pydantic import BaseModel, validator
import re

class ProductionPlanRequest(BaseModel):
    year: int
    month: int
    planning_horizon_days: int
    optimization_objectives: dict

    @validator('year')
    def validate_year(cls, v):
        current_year = datetime.now().year
        if not (current_year - 1 <= v <= current_year + 5):
            raise ValueError('年份必须在合理范围内')
        return v

    @validator('month')
    def validate_month(cls, v):
        if not (1 <= v <= 12):
            raise ValueError('月份必须在1-12之间')
        return v

    @validator('planning_horizon_days')
    def validate_horizon(cls, v):
        if not (1 <= v <= 365):
            raise ValueError('规划周期必须在1-365天之间')
        return v

# SQL注入防护
class SafeQueryBuilder:
    def __init__(self, db_session):
        self.session = db_session

    def get_equipment_by_type(self, equipment_type: str):
        # 使用参数化查询防止SQL注入
        query = """
            SELECT * FROM equipment
            WHERE equipment_type = :type AND status = 1
        """
        return self.session.execute(query, {"type": equipment_type}).fetchall()

    def search_plans(self, search_term: str):
        # 清理搜索词
        clean_term = re.sub(r'[^\w\s-]', '', search_term)

        query = """
            SELECT * FROM production_plans
            WHERE plan_name LIKE :search_term
            LIMIT 100
        """
        return self.session.execute(
            query,
            {"search_term": f"%{clean_term}%"}
        ).fetchall()
```

### 2. 数据保护

#### 敏感数据加密
```python
from cryptography.fernet import Fernet
import os
import base64

class DataProtection:
    def __init__(self):
        # 从环境变量或密钥管理服务获取密钥
        key = os.getenv('ENCRYPTION_KEY')
        if not key:
            raise ValueError("未找到加密密钥")

        self.cipher = Fernet(key.encode())

    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        encrypted_data = self.cipher.encrypt(data.encode())
        return base64.b64encode(encrypted_data).decode()

    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        encrypted_bytes = base64.b64decode(encrypted_data.encode())
        decrypted_data = self.cipher.decrypt(encrypted_bytes)
        return decrypted_data.decode()

    def hash_password(self, password: str) -> str:
        """密码哈希"""
        import bcrypt

        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')

    def verify_password(self, password: str, hashed: str) -> bool:
        """验证密码"""
        import bcrypt

        return bcrypt.checkpw(
            password.encode('utf-8'),
            hashed.encode('utf-8')
        )

# 数据脱敏
class DataMasking:
    def mask_email(self, email: str) -> str:
        """邮箱脱敏"""
        if '@' not in email:
            return email

        local, domain = email.split('@')
        if len(local) <= 2:
            return email

        masked_local = local[0] + '*' * (len(local) - 2) + local[-1]
        return f"{masked_local}@{domain}"

    def mask_phone(self, phone: str) -> str:
        """手机号脱敏"""
        if len(phone) != 11:
            return phone

        return phone[:3] + '****' + phone[7:]

    def mask_sensitive_logs(self, log_data: dict) -> dict:
        """日志脱敏"""
        sensitive_fields = ['password', 'token', 'api_key', 'email', 'phone']

        masked_data = log_data.copy()
        for field in sensitive_fields:
            if field in masked_data:
                if field == 'email':
                    masked_data[field] = self.mask_email(masked_data[field])
                elif field == 'phone':
                    masked_data[field] = self.mask_phone(masked_data[field])
                else:
                    masked_data[field] = '***MASKED***'

        return masked_data
```

这个技术评估与最佳实践文档涵盖了技术选型、架构设计、性能优化和安全实践等关键方面。
